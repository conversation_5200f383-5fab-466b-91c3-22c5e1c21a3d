import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'dart:convert';
import '../models/note.dart';
import '../utils/date_formatter.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  NotificationService._internal();

  Future<void> initialize() async {
    // Initialize timezone data
    tz_data.initializeTimeZones();

    // Initialize notification settings
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Request permission (iOS and newer Android versions)
    await requestPermissions();
  }

  // Handle notification taps
  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap - can be used to navigate to specific note
    // The response.payload would contain the note ID
  }

  Future<void> requestPermissions() async {
    // For iOS
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );

    // For Android - wrapped in try-catch to handle API differences
    try {
      // Try to get the Android implementation
      final androidImplementation = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
              
      if (androidImplementation != null) {
        try {
          await androidImplementation.requestNotificationsPermission();
        } catch (error) {
          debugPrint('Android notification permission error: $error');
          // Old API might not have this method
        }
      }
    } catch (e) {
      debugPrint('Android notification permission setup error: $e');
    }
  }

  // Schedule a notification for a note's reminder
  Future<void> scheduleNoteReminder(Note note) async {
    if (!note.hasReminder || note.reminderTime == null) {
      return;
    }

    // Cancel any existing notification for this note
    await cancelNoteReminder(note.id!);

    // Skip if reminder time is in the past and it's not a recurring reminder
    if (note.reminderTime!.isBefore(DateTime.now()) && 
        note.reminderRepeatType == ReminderRepeatType.none) {
      return;
    }

    // Create the notification content
    AndroidNotificationDetails androidDetails = const AndroidNotificationDetails(
      'note_reminders_channel',
      'Note Reminders',
      channelDescription: 'Notifications for note reminders',
      importance: Importance.high,
      priority: Priority.high,
      styleInformation: BigTextStyleInformation(''),
    );

    DarwinNotificationDetails iosDetails = const DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    NotificationDetails platformDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // The title is the note's title or "Reminder" if title is empty
    final title = note.title.isNotEmpty ? note.title : 'Reminder';
    
    // For the body, show a preview of the note content (plain text)
    final bodyText = _getPlainTextFromNoteContent(note.content);
    final body = bodyText.isNotEmpty 
        ? (bodyText.length > 100 ? '${bodyText.substring(0, 97)}...' : bodyText) 
        : 'You have a note reminder';

    // Add repeat information to the notification body if it's recurring
    final String repeatInfo = note.reminderRepeatType != ReminderRepeatType.none 
        ? ' (${DateFormatter.formatRepeatType(note.reminderRepeatType)})' 
        : '';
    final String finalBody = '$body$repeatInfo';

    // Calculate the notification time
    tz.TZDateTime notificationTime = _getNextReminderTime(note);

    // Determine if we need to set up a recurring notification
    DateTimeComponents? dateTimeComponents;
    switch (note.reminderRepeatType) {
      case ReminderRepeatType.daily:
        dateTimeComponents = DateTimeComponents.time;
        break;
      case ReminderRepeatType.weekly:
        dateTimeComponents = DateTimeComponents.dayOfWeekAndTime;
        break;
      case ReminderRepeatType.monthly:
        dateTimeComponents = DateTimeComponents.dayOfMonthAndTime;
        break;
      case ReminderRepeatType.yearly:
        dateTimeComponents = DateTimeComponents.dateAndTime;
        break;
      case ReminderRepeatType.none:
      default:
        dateTimeComponents = null;
        break;
    }

    // Schedule the notification
    await _flutterLocalNotificationsPlugin.zonedSchedule(
      note.id!, // Use note ID as notification ID
      title,
      finalBody,
      notificationTime,
      platformDetails,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      matchDateTimeComponents: dateTimeComponents,
      payload: note.id.toString(), // Pass note ID as payload
    );
  }

  // Calculate the next reminder time based on repeat type
  tz.TZDateTime _getNextReminderTime(Note note) {
    // Convert reminder time to TZDateTime
    tz.TZDateTime scheduledDate = tz.TZDateTime.from(note.reminderTime!, tz.local);
    
    // If the time is in the past and it's a recurring reminder, 
    // calculate the next occurrence
    if (scheduledDate.isBefore(tz.TZDateTime.now(tz.local)) && 
        note.reminderRepeatType != ReminderRepeatType.none) {
      final now = tz.TZDateTime.now(tz.local);
      
      switch (note.reminderRepeatType) {
        case ReminderRepeatType.daily:
          scheduledDate = tz.TZDateTime(
            tz.local,
            now.year,
            now.month,
            now.day,
            scheduledDate.hour,
            scheduledDate.minute,
            scheduledDate.second,
          );
          if (scheduledDate.isBefore(now)) {
            scheduledDate = scheduledDate.add(const Duration(days: 1));
          }
          break;
          
        case ReminderRepeatType.weekly:
          // Calculate days until the next occurrence of this weekday
          int daysUntilTargetDay = scheduledDate.weekday - now.weekday;
          if (daysUntilTargetDay < 0) daysUntilTargetDay += 7;
          else if (daysUntilTargetDay == 0 && 
              tz.TZDateTime(
                tz.local,
                now.year,
                now.month,
                now.day,
                scheduledDate.hour,
                scheduledDate.minute,
              ).isBefore(now)) {
            daysUntilTargetDay = 7;
          }
          
          scheduledDate = tz.TZDateTime(
            tz.local,
            now.year,
            now.month,
            now.day + daysUntilTargetDay,
            scheduledDate.hour,
            scheduledDate.minute,
            scheduledDate.second,
          );
          break;
          
        case ReminderRepeatType.monthly:
          // Find the next month with the same day
          scheduledDate = tz.TZDateTime(
            tz.local,
            now.year,
            now.month,
            scheduledDate.day <= 28 ? scheduledDate.day : 28, // Safeguard for month length
            scheduledDate.hour,
            scheduledDate.minute,
            scheduledDate.second,
          );
          if (scheduledDate.isBefore(now)) {
            // Move to next month
            scheduledDate = tz.TZDateTime(
              tz.local,
              now.year,
              now.month + 1,
              scheduledDate.day <= 28 ? scheduledDate.day : 28,
              scheduledDate.hour,
              scheduledDate.minute,
              scheduledDate.second,
            );
          }
          break;
          
        case ReminderRepeatType.yearly:
          // Find the next year with same month and day
          scheduledDate = tz.TZDateTime(
            tz.local,
            now.year,
            scheduledDate.month,
            // Safeguard for Feb 29 on non-leap years
            (scheduledDate.month == 2 && scheduledDate.day == 29 && !_isLeapYear(now.year))
                ? 28
                : scheduledDate.day,
            scheduledDate.hour,
            scheduledDate.minute,
            scheduledDate.second,
          );
          if (scheduledDate.isBefore(now)) {
            // Move to next year
            scheduledDate = tz.TZDateTime(
              tz.local,
              now.year + 1,
              scheduledDate.month,
              // Safeguard for Feb 29 on non-leap years
              (scheduledDate.month == 2 && scheduledDate.day == 29 && !_isLeapYear(now.year + 1))
                  ? 28
                  : scheduledDate.day,
              scheduledDate.hour,
              scheduledDate.minute,
              scheduledDate.second,
            );
          }
          break;
          
        case ReminderRepeatType.none:
        default:
          // No repeat, just use the original time
          break;
      }
    }
    
    return scheduledDate;
  }
  
  // Helper method to check if a year is a leap year
  bool _isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }

  // Cancel a scheduled notification
  Future<void> cancelNoteReminder(int noteId) async {
    await _flutterLocalNotificationsPlugin.cancel(noteId);
  }

  // Cancel all scheduled notifications
  Future<void> cancelAllNoteReminders() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  // Extract plain text from the note's Delta content JSON
  String _getPlainTextFromNoteContent(String content) {
    try {
      // Parse the JSON content (Quill Delta format)
      final List<dynamic> delta = json.decode(content);
      
      // Extract text from insert operations
      String plainText = '';
      for (final op in delta) {
        if (op.containsKey('insert')) {
          if (op['insert'] is String) {
            plainText += op['insert'];
          } else if (op['insert'] is Map) {
            // Handle embeds like images or other non-text content
            plainText += '[attachment]';
          }
        }
      }
      
      // Clean up the text (remove excess whitespace, etc.)
      plainText = plainText.trim().replaceAll(RegExp(r'\s+'), ' ');
      
      return plainText;
    } catch (e) {
      // Fallback to simple regex cleanup if JSON parsing fails
      return content
          .replaceAll(RegExp(r'[{}\[\]"\\]'), ' ')
          .replaceAll('insert:', '')
          .replaceAll(',attributes:', '')
          .replaceAll(RegExp(r'\s+'), ' ')
          .trim();
    }
  }
} 