import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/label.dart';
import '../providers/labels_provider.dart';
import '../utils/constants.dart';

class LabelsScreen extends StatelessWidget {
  static const routeName = '/labels';

  const LabelsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Labels'),
        elevation: 0,
      ),
      body: Consumer<LabelsProvider>(
        builder: (context, labelsProvider, child) {
          if (labelsProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final labels = labelsProvider.labels;

          if (labels.isEmpty) {
            return const Center(
              child: Text('No labels yet. Create one by tapping the + button.'),
            );
          }

          return ListView.builder(
            itemCount: labels.length,
            itemBuilder: (context, index) {
              final label = labels[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: label.color,
                  radius: 12,
                ),
                title: Text(label.name),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _showLabelDialog(context, label),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () => _confirmDelete(context, label),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColors.accent,
        child: const Icon(Icons.add),
        onPressed: () => _showLabelDialog(context, null),
      ),
    );
  }

  void _showLabelDialog(BuildContext context, Label? existingLabel) {
    final textController = TextEditingController(text: existingLabel?.name ?? '');
    Color selectedColor = existingLabel?.color ?? AppColors.noteColors.first;

    showDialog(
      context: context,
      builder: (ctx) => StatefulBuilder(
        builder: (ctx, setState) {
          return AlertDialog(
            title: Text(existingLabel == null ? 'Add Label' : 'Edit Label'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: textController,
                  decoration: const InputDecoration(
                    labelText: 'Label Name',
                  ),
                  autofocus: true,
                ),
                const SizedBox(height: 16),
                const Text('Select Color'),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: AppColors.noteColors.map((color) {
                    final isSelected = selectedColor.value == color.value;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedColor = color;
                        });
                      },
                      child: Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected ? Colors.white : Colors.transparent,
                            width: 2,
                          ),
                        ),
                        child: isSelected
                            ? const Icon(Icons.check, color: Colors.white, size: 20)
                            : null,
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(ctx).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  final name = textController.text.trim();
                  if (name.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Please enter a name')),
                    );
                    return;
                  }

                  final labelsProvider = Provider.of<LabelsProvider>(context, listen: false);
                  
                  if (existingLabel == null) {
                    final newLabel = Label(
                      name: name,
                      color: selectedColor,
                    );
                    labelsProvider.addLabel(newLabel);
                  } else {
                    final updatedLabel = existingLabel.copyWith(
                      name: name,
                      color: selectedColor,
                    );
                    labelsProvider.updateLabel(updatedLabel);
                  }
                  
                  Navigator.of(ctx).pop();
                },
                child: const Text('Save'),
              ),
            ],
          );
        }
      ),
    );
  }

  void _confirmDelete(BuildContext context, Label label) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Delete Label'),
        content: Text('Are you sure you want to delete "${label.name}"? This will remove the label from all notes.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<LabelsProvider>(context, listen: false)
                .deleteLabel(label.id!);
              Navigator.of(ctx).pop();
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
} 