import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/constants.dart';

enum PinEntryMode {
  setup,    // Creating a new PIN
  verify,   // Verifying existing PIN 
  change    // Changing PIN
}

class SimplifiedPinEntry extends StatefulWidget {
  final PinEntryMode mode;
  final String? title;
  final String? subtitle;
  final int pinLength;
  final Function(String) onPinEntered;
  final Function()? onCancelled;
  final bool showBiometricButton;
  final Function()? onBiometricPressed;
  final bool hideAppBar; // New parameter to control app bar visibility

  const SimplifiedPinEntry({
    Key? key,
    required this.mode,
    this.title,
    this.subtitle,
    this.pinLength = 4,
    required this.onPinEntered,
    this.onCancelled,
    this.showBiometricButton = false,
    this.onBiometricPressed,
    this.hideAppBar = false, // Default to showing app bar
  }) : super(key: key);

  @override
  State<SimplifiedPinEntry> createState() => _SimplifiedPinEntryState();
}

class _SimplifiedPinEntryState extends State<SimplifiedPinEntry> {
  String _currentPin = '';
  String? _pin;
  String? _confirmPin;
  String? _errorText;
  bool _obscurePin = true;

  String _getTitle() {
    if (widget.title != null) return widget.title!;
    
    switch (widget.mode) {
      case PinEntryMode.setup:
        return _pin == null ? 'Set Up PIN' : 'Confirm PIN';
      case PinEntryMode.verify:
        return 'Enter PIN';
      case PinEntryMode.change:
        if (_pin == null) {
          return 'Enter Current PIN';
        } else if (_confirmPin == null) {
          return 'Enter New PIN';
        } else {
          return 'Confirm New PIN';
        }
    }
  }

  String _getSubtitle() {
    if (widget.subtitle != null) return widget.subtitle!;
    
    switch (widget.mode) {
      case PinEntryMode.setup:
        return _pin == null 
            ? 'Create a PIN to secure your notes' 
            : 'Re-enter the same PIN to confirm';
      case PinEntryMode.verify:
        return 'Enter your PIN to unlock the app';
      case PinEntryMode.change:
        if (_pin == null) {
          return 'Enter your current PIN';
        } else if (_confirmPin == null) {
          return 'Create a new PIN';
        } else {
          return 'Re-enter your new PIN to confirm';
        }
    }
  }

  void _handleKeyPressed(String digit) {
    if (_currentPin.length < widget.pinLength) {
      setState(() {
        _currentPin += digit;
      });
      
      // If we've reached the PIN length, process it
      if (_currentPin.length == widget.pinLength) {
        _processPinEntry(_currentPin);
      }
    }
  }

  void _handleBackspace() {
    if (_currentPin.isNotEmpty) {
      setState(() {
        _currentPin = _currentPin.substring(0, _currentPin.length - 1);
      });
    }
  }

  // Reset pin is handled directly in the clear button and other functions

  void _processPinEntry(String pin) {
    setState(() {
      _errorText = null;
    });

    switch (widget.mode) {
      case PinEntryMode.setup:
        if (_pin == null) {
          // First entry - store PIN and ask for confirmation
          setState(() {
            _pin = pin;
            _currentPin = '';
          });
        } else {
          // Confirmation entry - check if PINs match
          if (_pin == pin) {
            widget.onPinEntered(pin);
          } else {
            setState(() {
              _errorText = 'PINs do not match. Please try again.';
              _pin = null;
              _currentPin = '';
            });
          }
        }
        break;

      case PinEntryMode.verify:
        // Just verify the PIN
        widget.onPinEntered(pin);
        break;

      case PinEntryMode.change:
        if (_pin == null) {
          // First entry - store current PIN for verification
          setState(() {
            _pin = pin;
            _currentPin = '';
          });
        } else if (_confirmPin == null) {
          // Second entry - store new PIN
          setState(() {
            _confirmPin = pin;
            _currentPin = '';
          });
        } else {
          // Third entry - confirm new PIN
          if (_confirmPin == pin) {
            // Return both old and new PIN
            widget.onPinEntered('$_pin|$pin');
          } else {
            setState(() {
              _errorText = 'New PINs do not match. Please try again.';
              _confirmPin = null;
              _currentPin = '';
            });
          }
        }
        break;
    }
  }

  Widget _buildPinDots() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          widget.pinLength,
          (index) {
            final isFilled = index < _currentPin.length;
            final isObscured = _obscurePin && isFilled;
            
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: isObscured ? BoxShape.circle : BoxShape.circle,
                border: Border.all(
                  color: isFilled ? AppColors.accent : Colors.grey.shade400,
                  width: 2,
                ),
                color: isFilled ? AppColors.accent : Colors.transparent,
              ),
              child: !_obscurePin && isFilled
                  ? Center(
                      child: Text(
                        _currentPin[index],
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  : null,
            );
          },
        ),
      ),
    );
  }

  Widget _buildKeypad({bool isCompactScreen = false, double keypadSpacing = 16.0}) {
    const keys = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['', '0', 'back'],
    ];

    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: isCompactScreen ? 240 : 280, // Limit maximum height
      ),
      child: Padding(
        padding: EdgeInsets.only(bottom: isCompactScreen ? 0 : 8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: keys.map((row) => _buildKeypadRow(
            row, 
            isCompactScreen: isCompactScreen,
            spacing: keypadSpacing
          )).toList(),
        ),
      ),
    );
  }

  Widget _buildKeypadRow(List<String> keys, {bool isCompactScreen = false, double spacing = 16.0}) {
    // Adapt button size based on screen size
    final buttonSize = isCompactScreen ? 55.0 : 65.0;
    final fontSize = isCompactScreen ? 18.0 : 22.0;
    
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isCompactScreen ? spacing / 3 : spacing / 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: keys.map((key) {
          if (key.isEmpty) {
            return SizedBox(width: buttonSize, height: buttonSize);
          } else if (key == 'back') {
            return SizedBox(
              width: buttonSize,
              height: buttonSize,
              child: IconButton(
                icon: const Icon(Icons.backspace_outlined),
                onPressed: _handleBackspace,
                padding: EdgeInsets.zero,
                iconSize: isCompactScreen ? 20 : 24,
                constraints: BoxConstraints(
                  minWidth: buttonSize,
                  minHeight: buttonSize,
                  maxWidth: buttonSize,
                  maxHeight: buttonSize,
                ),
              ),
            );
          } else {
            return SizedBox(
              width: buttonSize,
              height: buttonSize,
              child: TextButton(
                onPressed: () => _handleKeyPressed(key),
                style: TextButton.styleFrom(
                  shape: const CircleBorder(),
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  minimumSize: Size.zero,
                ),
                child: Text(
                  key,
                  style: TextStyle(fontSize: fontSize),
                ),
              ),
            );
          }
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive sizes based on available height
        final availableHeight = constraints.maxHeight;
        final isSmallScreen = availableHeight < 600;
        final isCompactScreen = availableHeight < 700;
        
        // Scale UI elements based on screen size
        final lockIconSize = isSmallScreen ? 40.0 : 64.0;
        final titleFontSize = isSmallScreen ? 20.0 : 24.0;
        final subtitleFontSize = isSmallScreen ? 14.0 : 16.0;
        final verticalSpacing = isCompactScreen ? 8.0 : 16.0;
        final keypadSpacing = isCompactScreen ? 8.0 : 16.0;
        
        return Scaffold(
          // Only show app bar if not hidden
          appBar: widget.hideAppBar ? null : AppBar(
            title: Text(_getTitle()),
            elevation: 0,
            centerTitle: true,
            leading: widget.onCancelled != null
                ? IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: widget.onCancelled,
                  )
                : null,
            actions: [
              // Toggle PIN visibility
              IconButton(
                icon: Icon(
                  _obscurePin ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePin = !_obscurePin;
                  });
                },
              ),
            ],
          ),
          body: SafeArea(
            bottom: true, // Ensure bottom is safe area aware
            minimum: EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: isCompactScreen ? 8.0 : 16.0
            ),
            child: Column(
              children: [
                // Top section with lock icon and descriptions
                Flexible(
                  flex: 3,
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        vertical: verticalSpacing,
                        horizontal: 16.0
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Add toggle visibility button at the top when app bar is hidden
                          if (widget.hideAppBar) 
                            Align(
                              alignment: Alignment.topRight,
                              child: IconButton(
                                icon: Icon(
                                  _obscurePin ? Icons.visibility : Icons.visibility_off,
                                  color: Theme.of(context).brightness == Brightness.dark 
                                      ? Colors.white70 
                                      : Colors.black54,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePin = !_obscurePin;
                                  });
                                },
                              ),
                            ),
                          Icon(
                            Icons.lock_outline,
                            size: lockIconSize,
                            color: AppColors.accent,
                          ),
                          SizedBox(height: verticalSpacing),
                          Text(
                            _getTitle(),
                            style: TextStyle(
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: verticalSpacing / 2),
                          Text(
                            _getSubtitle(),
                            style: TextStyle(
                              fontSize: subtitleFontSize,
                              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: verticalSpacing * 1.5),
                          if (widget.showBiometricButton && widget.onBiometricPressed != null) ...[                  
                            IconButton(
                              icon: Icon(Icons.fingerprint, size: isSmallScreen ? 30 : 40),
                              onPressed: widget.onBiometricPressed,
                              tooltip: 'Use biometric authentication',
                              color: AppColors.accent,
                              padding: EdgeInsets.zero,
                            ),
                            SizedBox(height: verticalSpacing / 2),
                            const Text('Or enter PIN', style: TextStyle(fontSize: 14)),
                            SizedBox(height: verticalSpacing / 2),
                          ],
                          _buildPinDots(),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // Error text if present
                if (_errorText != null) ...[              
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: verticalSpacing / 2),
                    child: Text(
                      _errorText!,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                        fontSize: subtitleFontSize,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
                
                // Flexible spacer that can compress if needed
                Flexible(
                  flex: 1,
                  child: Container(),
                ),
                
                // Keypad with adaptive sizing
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: _buildKeypad(isCompactScreen: isCompactScreen, keypadSpacing: keypadSpacing),
                ),
                
                // Clear button
                Padding(
                  padding: EdgeInsets.only(bottom: 4.0),
                  child: TextButton(
                    onPressed: () {
                      setState(() {
                        _currentPin = '';
                        _pin = null;
                        _confirmPin = null;
                        _errorText = null;
                      });
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      'Clear',
                      style: AppTextStyles.button.copyWith(color: AppColors.accent),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
