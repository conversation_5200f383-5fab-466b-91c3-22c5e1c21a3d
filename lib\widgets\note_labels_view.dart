import 'package:flutter/material.dart';
import '../models/label.dart';

class NoteLabelsView extends StatelessWidget {
  final List<Label> labels;
  final double chipHeight;
  final EdgeInsetsGeometry? padding;

  const NoteLabelsView({
    Key? key,
    required this.labels,
    this.chipHeight = 24,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (labels.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: padding ?? const EdgeInsets.only(top: 4),
      child: Wrap(
        spacing: 4,
        runSpacing: 4,
        children: labels.map((label) {
          return Container(
            height: chipHeight,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              color: label.color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(chipHeight / 2),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircleAvatar(
                  backgroundColor: label.color,
                  radius: chipHeight / 5,
                ),
                const SizedBox(width: 4),
                Text(
                  label.name,
                  style: TextStyle(
                    fontSize: chipHeight * 0.5,
                    color: label.color,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
} 