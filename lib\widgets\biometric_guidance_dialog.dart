import 'package:flutter/material.dart';
import 'dart:io';
import 'package:flutter/services.dart';

/// A dialog that guides users to set up biometrics at the OS level
class BiometricGuidanceDialog extends StatelessWidget {
  final Function()? onDismiss;

  const BiometricGuidanceDialog({
    Key? key,
    this.onDismiss,
  }) : super(key: key);
  
  // Method to open device settings
  Future<void> _openDeviceSettings() async {
    try {
      if (Platform.isAndroid) {
        // For Android, we can try to open the biometric settings directly
        await SystemNavigator.pop();
        
        // Note: This is a workaround, as Flutter doesn't provide direct access
        // to open specific system settings without using plugins
        // After system navigator pop, the user needs to go to settings manually
      } else if (Platform.isIOS) {
        // For iOS, this will open the device settings
        await SystemNavigator.pop();
      }
    } catch (e) {
      // Handle any errors that might occur
      debugPrint('Error opening settings: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Show different instructions based on platform
    final String instructionTitle = Platform.isIOS 
        ? 'Set Up Face ID/Touch ID'
        : 'Set Up Fingerprint/Face Unlock';
    
    final String instructionSteps = Platform.isIOS
        ? '1. Open Settings\n'
          '2. Tap Face ID & Passcode (or Touch ID & Passcode)\n'
          '3. Enter your device passcode\n'
          '4. Set up Face ID or Touch ID\n'
          '5. Follow the on-screen instructions to register your biometrics'
        : '1. Open Settings\n'
          '2. Tap Security & privacy\n'
          '3. Tap Fingerprint or Face unlock\n'
          '4. Enter your PIN/password if prompted\n'
          '5. Follow the on-screen instructions to register your fingerprint/face';
    
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      title: Column(
        children: [
          Icon(
            Icons.fingerprint,
            size: 48,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 12),
          Text(
            'Biometric Setup Required',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'You need to register your fingerprint/face in your device settings before using biometric authentication.',
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.white70 : Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Text(
            instructionTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            instructionSteps,
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.white70 : Colors.black87,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 20,
                  color: Theme.of(context).colorScheme.onSecondaryContainer,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'After registering your biometrics, return to Dark Notes to complete the setup.',
                    style: TextStyle(
                      fontSize: 13,
                      color: Theme.of(context).colorScheme.onSecondaryContainer,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        OutlinedButton.icon(
          icon: const Icon(Icons.settings),
          label: const Text('OPEN SETTINGS'),
          style: OutlinedButton.styleFrom(
            foregroundColor: Theme.of(context).primaryColor,
          ),
          onPressed: () async {
            // Try to open device settings
            Navigator.pop(context);
            await _openDeviceSettings();
            if (onDismiss != null) {
              onDismiss!();
            }
          },
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            if (onDismiss != null) {
              onDismiss!();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: const Text('I\'LL DO IT LATER'),
        ),
      ],
    );
  }
} 