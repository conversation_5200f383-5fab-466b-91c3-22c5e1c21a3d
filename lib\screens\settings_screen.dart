import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/app_lock_provider.dart';
import '../providers/notes_provider.dart';
import '../utils/constants.dart';
import '../services/memory_service.dart';
import '../services/unified_backup_service.dart';
import 'app_lock_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isLoading = false;
  bool _encryptBackup = false;
  final UnifiedBackupService _backupService = UnifiedBackupService();

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;
    
    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
      appBar: AppBar(
        title: const Text('Settings'),
        centerTitle: false,
        elevation: 0,
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: AppColors.accent,
              ),
            )
          : ListView(
              children: [
                // Appearance
                _buildSectionHeader('Appearance'),
                _buildToggleOption(
                  icon: isDarkMode ? Icons.dark_mode : Icons.light_mode,
                  title: 'Dark Mode',
                  subtitle: 'Toggle between dark and light theme',
                  value: isDarkMode,
                  onChanged: (value) {
                    themeProvider.toggleTheme();
                  },
                ),
                const Divider(),
                
                // Security
                _buildSectionHeader('Security'),
                _buildActionOption(
                  icon: Icons.lock_outline,
                  title: 'App Lock',
                  subtitle: 'Configure PIN, biometrics and app security',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AppLockSettingsScreen(),
                      ),
                    );
                  },
                ),
                const Divider(),
                
                // Backup & Restore
                _buildSectionHeader('Data Management'),
                _buildActionOption(
                  icon: Icons.backup_outlined,
                  title: 'Backup & Restore',
                  subtitle: 'Backup your notes or restore from backup',
                  onTap: () {
                    _showBackupRestoreDialog();
                  },
                ),
                
                // Memory Management (Simplified)
                _buildActionOption(
                  icon: Icons.memory_outlined,
                  title: 'Memory Management',
                  subtitle: 'Clear cache and optimize memory usage',
                  onTap: () {
                    _showMemoryManagementDialog();
                  },
                ),
                
                // Help and About
                _buildSectionHeader('About'),
                _buildActionOption(
                  icon: Icons.info_outline,
                  title: 'About Dark Notes',
                  subtitle: 'App version and information',
                  onTap: () {
                    // Show simple about dialog
                    showAboutDialog(
                      context: context,
                      applicationName: 'Dark Notes',
                      applicationVersion: '1.0.0',
                      applicationIcon: Icon(
                        Icons.note_alt,
                        color: AppColors.accent,
                        size: 48,
                      ),
                      applicationLegalese: '© 2023 Dark Notes',
                    );
                  },
                ),
              ],
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = isDarkMode ? Colors.white70 : Colors.black54;
    
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        top: 24,
        right: 16,
        bottom: 8,
      ),
      child: Text(
        title.toUpperCase(),
        style: AppTextStyles.caption.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
          letterSpacing: 1.2,
        ),
      ),
    );
  }

  Widget _buildToggleOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.accent.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.accent,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.subtitle.copyWith(
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.caption.copyWith(
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.accent,
      ),
    );
  }

  Widget _buildActionOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.accent.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.accent,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.subtitle.copyWith(
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.caption.copyWith(
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
      ),
      onTap: onTap,
    );
  }
  
  // Show an enhanced dialog with memory management options
  void _showMemoryManagementDialog() {
    final memoryService = Provider.of<MemoryService>(context, listen: false);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.memory_outlined,
              color: AppColors.accent,
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text('Memory Management'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'What would you like to clear?',
              style: TextStyle(
                color: isDarkMode ? Colors.white70 : Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            _buildMemoryOption(
              context: context,
              icon: Icons.note_outlined,
              title: 'Note Cache',
              subtitle: 'Clear cached note data',
              onTap: () {
                memoryService.clearCache();
                Navigator.pop(context);
                _showSuccessSnackbar('Note cache cleared');
              },
            ),
            const SizedBox(height: 8),
            _buildMemoryOption(
              context: context,
              icon: Icons.image_outlined,
              title: 'Image Cache',
              subtitle: 'Clear cached images',
              onTap: () {
                memoryService.clearImageCache();
                Navigator.pop(context);
                _showSuccessSnackbar('Image cache cleared');
              },
            ),
            const SizedBox(height: 8),
            _buildMemoryOption(
              context: context,
              icon: Icons.delete_sweep_outlined,
              title: 'All Cache',
              subtitle: 'Clear all cached data',
              onTap: () {
                memoryService.clearCache();
                memoryService.clearImageCache();
                Navigator.pop(context);
                _showSuccessSnackbar('All caches cleared');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppColors.accent),
            ),
          ),
        ],
      ),
    );
  }
  
  // Show backup and restore dialog
  void _showBackupRestoreDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(
                  Icons.backup_outlined,
                  color: AppColors.accent,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text('Backup & Restore'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Choose an option:',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                
                // Encrypt backup toggle
                SwitchListTile(
                  title: const Text('Encrypt Backup'),
                  subtitle: Text(
                    appLockProvider.isLockEnabled
                        ? 'Uses your app PIN for encryption'
                        : 'You\'ll create a password',
                    style: TextStyle(
                      fontSize: 12,
                      color: isDarkMode ? Colors.white60 : Colors.black54,
                    ),
                  ),
                  value: _encryptBackup,
                  activeColor: AppColors.accent,
                  contentPadding: EdgeInsets.zero,
                  onChanged: (value) {
                    setState(() {
                      _encryptBackup = value;
                    });
                  },
                ),
                
                const SizedBox(height: 16),
                _buildBackupOption(
                  context: context,
                  icon: Icons.save_outlined,
                  title: 'Create Backup',
                  subtitle: 'Save your notes to a file',
                  color: AppColors.accent,
                  onTap: () async {
                    Navigator.pop(context);
                    await _createBackup(appLockProvider);
                  },
                ),
                const SizedBox(height: 16),
                _buildBackupOption(
                  context: context,
                  icon: Icons.restore_outlined,
                  title: 'Restore Backup',
                  subtitle: 'Restore notes from a file',
                  color: Colors.deepOrange,
                  onTap: () async {
                    Navigator.pop(context);
                    await _restoreBackup(appLockProvider);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: TextStyle(color: AppColors.accent),
                ),
              ),
            ],
          );
        }
      ),
    );
  }
  
  Widget _buildMemoryOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.accent.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: AppColors.accent,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black87,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildBackupOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black87,
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  void _showSuccessSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green[700],
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  Future<void> _createBackup(AppLockProvider appLockProvider) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final String? oneTimePassword = !appLockProvider.isLockEnabled && _encryptBackup
          ? await _promptForPassword()
          : null;

      if (!appLockProvider.isLockEnabled && _encryptBackup && oneTimePassword == null) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackbar("Backup cancelled: Password required for encryption");
        return;
      }

      final result = await _backupService.createBackup(
        context,
        _encryptBackup,
        appLockProvider,
        oneTimePassword: oneTimePassword,
      );

      setState(() {
        _isLoading = false;
      });
      
      if (result) {
        _showSuccessSnackbar("Backup created successfully");
      } else {
        _showErrorSnackbar("Failed to create backup");
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackbar("Error: $e");
    }
  }

  Future<void> _restoreBackup(AppLockProvider appLockProvider) async {
    // Show confirmation dialog
    final confirmRestore = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Warning'),
        content: const Text(
            'This will replace all your current notes with the ones from the backup. This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Restore', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmRestore != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final String? oneTimePassword = !appLockProvider.isLockEnabled
          ? await _promptForPassword()
          : null;

      final result = await _backupService.restoreBackup(
        context,
        appLockProvider,
        oneTimePassword: oneTimePassword,
      );

      setState(() {
        _isLoading = false;
      });
      
      if (result.contains("successfully")) {
        _showSuccessSnackbar(result);
        // Refresh notes if restore was successful
        final notesProvider = Provider.of<NotesProvider>(context, listen: false);
        await notesProvider.fetchNotes();
      } else {
        _showErrorSnackbar(result);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackbar("Error: $e");
    }
  }
  
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red[700],
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
  
  Future<String?> _promptForPassword() async {
    String? password;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Enter Password'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Enter a password for your backup'),
            const SizedBox(height: 16),
            TextField(
              autofocus: true,
              obscureText: true,
              decoration: const InputDecoration(
                hintText: 'Password',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                password = value;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (password != null && password!.isNotEmpty) {
                Navigator.pop(context, true);
              }
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );

    return password != null && password!.isNotEmpty ? password : null;
  }
}
