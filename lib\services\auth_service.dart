import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'dart:async';

/// Service for handling biometric authentication
class AuthService {
  final LocalAuthentication _localAuth;

  AuthService() : _localAuth = LocalAuthentication();

  /// Checks if device supports biometric authentication
  Future<bool> isBiometricAvailable() async {
    try {
      // Check if biometrics are available on the device
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      print("DEBUG: Device supports biometrics: $isDeviceSupported");
      return isDeviceSupported;
    } on PlatformException catch (e) {
      print("DEBUG: Error checking biometric availability: ${e.code} - ${e.message}");
      return false;
    }
  }

  /// Checks if biometrics are properly enrolled on the device
  Future<BiometricStatus> getBiometricStatus() async {
    try {
      // First check if the device supports biometrics
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      print("DEBUG: Device supports biometrics: $isDeviceSupported");
      if (!isDeviceSupported) {
        return BiometricStatus.unsupported;
      }

      // Then check if biometrics are available (enrolled)
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      print("DEBUG: Can check biometrics: $canCheckBiometrics");
      if (!canCheckBiometrics) {
        return BiometricStatus.notEnrolled;
      }

      // Get available biometrics to confirm enrollment
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      print("DEBUG: Available biometrics: $availableBiometrics");
      if (availableBiometrics.isEmpty) {
        return BiometricStatus.notEnrolled;
      }

      print("DEBUG: Biometrics are properly enrolled");
      return BiometricStatus.enrolled;
    } on PlatformException catch (e) {
      print("DEBUG: Error getting biometric status: ${e.code} - ${e.message}");
      return BiometricStatus.error;
    }
  }

  /// Gets available biometric types (fingerprint, face, etc.)
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      final biometrics = await _localAuth.getAvailableBiometrics();
      print("DEBUG: Available biometrics: $biometrics");
      return biometrics;
    } on PlatformException catch (e) {
      print("DEBUG: Error getting available biometrics: ${e.code} - ${e.message}");
      return [];
    }
  }

  /// Authenticates the user using biometrics
  Future<AuthResult> authenticateWithBiometrics({
    required String localizedReason,
  }) async {
    print("DEBUG: AuthService: Starting biometric authentication");
    
    try {
      // Check if biometrics are available right before authentication
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      
      print("DEBUG: AuthService: Pre-auth check - isDeviceSupported: $isDeviceSupported, canCheckBiometrics: $canCheckBiometrics");
      print("DEBUG: AuthService: Pre-auth check - availableBiometrics: $availableBiometrics");
      
      if (!isDeviceSupported || !canCheckBiometrics || availableBiometrics.isEmpty) {
        print("DEBUG: AuthService: Device not ready for biometric authentication");
        return AuthResult.notAvailable;
      }
      
      // CRITICAL FIX: Use biometricOnly: true to prevent system PIN fallback
      // This ensures better handling of authentication results
      final options = const AuthenticationOptions(
        stickyAuth: true,  // Make auth sticky to prevent timeouts
        biometricOnly: true,  // Force biometric only to prevent ambiguous results
        useErrorDialogs: true,
      );
      
      print("DEBUG: AuthService: Using IMPROVED authentication options: stickyAuth=true, biometricOnly=true");
      
      // Try authentication with improved error handling
      try {
        // Direct authentication attempt
        final authResult = await _localAuth.authenticate(
          localizedReason: localizedReason,
          options: options,
        );
        
        print("DEBUG: AuthService: Raw authentication result from platform: $authResult");
        
        // If authResult is true, authentication succeeded
        if (authResult) {
          print("DEBUG: AuthService: Authentication SUCCEEDED!");
          return AuthResult.success;
        } else {
          print("DEBUG: AuthService: Authentication CANCELED by user");
          return AuthResult.canceled;
        }
      } catch (innerError) {
        print("DEBUG: AuthService: Inner authentication error: $innerError");
        if (innerError is PlatformException) {
          print("DEBUG: AuthService: Platform exception code: ${innerError.code}");
          if (innerError.code == auth_error.notAvailable ||
              innerError.code == auth_error.notEnrolled ||
              innerError.code == auth_error.passcodeNotSet) {
            return AuthResult.notAvailable;
          }
          
          if (innerError.code == auth_error.lockedOut ||
              innerError.code == auth_error.permanentlyLockedOut) {
            return AuthResult.lockedOut;
          }
        }
        return AuthResult.error;
      }
    } on PlatformException catch (e) {
      print("DEBUG: AuthService: Platform Exception during authentication: ${e.code} - ${e.message}");
      if (e.code == auth_error.notAvailable ||
          e.code == auth_error.notEnrolled ||
          e.code == auth_error.passcodeNotSet) {
        return AuthResult.notAvailable;
      }
      
      if (e.code == auth_error.lockedOut ||
          e.code == auth_error.permanentlyLockedOut) {
        return AuthResult.lockedOut;
      }
      
      return AuthResult.error;
    } catch (e) {
      print("DEBUG: AuthService: Unexpected error during authentication: $e");
      return AuthResult.error;
    } finally {
      print("DEBUG: AuthService: Authentication process completed");
    }
  }
}

/// Authentication result with better cancellation handling
enum AuthResult {
  /// Authentication succeeded
  success,
  
  /// User canceled authentication
  canceled,
  
  /// Biometrics not available or not enrolled
  notAvailable,
  
  /// Too many failed attempts, biometrics locked
  lockedOut,
  
  /// Error during authentication
  error
}

/// Status of biometric availability
enum BiometricStatus {
  /// Device supports biometrics and user has enrolled them
  enrolled,
  
  /// Device supports biometrics but user hasn't enrolled them
  notEnrolled,
  
  /// Device doesn't support biometrics
  unsupported,
  
  /// Error checking biometric status
  error
}
