# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\pyflutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\pyflutter\\projects\\dark_notes" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\pyflutter\\flutter"
  "PROJECT_DIR=C:\\pyflutter\\projects\\dark_notes"
  "FLUTTER_ROOT=C:\\pyflutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\pyflutter\\projects\\dark_notes\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\pyflutter\\projects\\dark_notes"
  "FLUTTER_TARGET=C:\\pyflutter\\projects\\dark_notes\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\pyflutter\\projects\\dark_notes\\.dart_tool\\package_config.json"
)
