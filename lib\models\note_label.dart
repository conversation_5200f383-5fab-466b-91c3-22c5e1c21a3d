class NoteLabel {
  final int? id;
  final int noteId;
  final int labelId;

  NoteLabel({
    this.id,
    required this.noteId,
    required this.labelId,
  });

  NoteLabel copyWith({
    int? id,
    int? noteId,
    int? labelId,
  }) {
    return NoteLabel(
      id: id ?? this.id,
      noteId: noteId ?? this.noteId,
      labelId: labelId ?? this.labelId,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'noteId': noteId,
      'labelId': labelId,
    };
  }

  factory NoteLabel.fromMap(Map<String, dynamic> map) {
    return NoteLabel(
      id: map['id'],
      noteId: map['noteId'],
      labelId: map['labelId'],
    );
  }

  @override
  String toString() {
    return 'NoteLabel(id: $id, noteId: $noteId, labelId: $labelId)';
  }
} 