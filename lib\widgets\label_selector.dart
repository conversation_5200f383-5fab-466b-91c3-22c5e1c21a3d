import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/label.dart';
import '../providers/labels_provider.dart';
import '../screens/labels_screen.dart';

class LabelSelector extends StatefulWidget {
  final int? noteId;
  final List<Label> selectedLabels;
  final Function(List<Label>) onLabelsChanged;

  const LabelSelector({
    Key? key,
    required this.noteId,
    required this.selectedLabels,
    required this.onLabelsChanged,
  }) : super(key: key);

  @override
  State<LabelSelector> createState() => _LabelSelectorState();
}

class _LabelSelectorState extends State<LabelSelector> {
  late List<Label> _selectedLabels;

  @override
  void initState() {
    super.initState();
    _selectedLabels = List.from(widget.selectedLabels);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Labels',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        _buildSelectedLabelsChips(),
        _buildLabelOptions(),
      ],
    );
  }

  Widget _buildSelectedLabelsChips() {
    if (_selectedLabels.isEmpty) {
      return const Padding(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Text('No labels selected'),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Wrap(
        spacing: 8,
        runSpacing: 4,
        children: _selectedLabels.map((label) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Chip(
              backgroundColor: label.color.withOpacity(0.2),
              labelStyle: TextStyle(color: label.color),
              label: Text(label.name),
              deleteIconColor: label.color,
              onDeleted: () {
                _removeLabel(label);
              },
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildLabelOptions() {
    return Consumer<LabelsProvider>(
      builder: (context, labelsProvider, child) {
        final allLabels = labelsProvider.labels;
        final availableLabels = allLabels.where(
          (label) => !_selectedLabels.any((selected) => selected.id == label.id)
        ).toList();

        if (availableLabels.isEmpty && allLabels.isEmpty) {
          return ListTile(
            leading: const Icon(Icons.add),
            title: const Text('Create a new label'),
            onTap: () => Navigator.of(context).pushNamed(LabelsScreen.routeName),
          );
        }

        if (availableLabels.isEmpty) {
          return const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text('All labels are already selected'),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text('Available Labels'),
            ),
            ...availableLabels.map((label) => _buildLabelTile(label)).toList(),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Manage all labels'),
              onTap: () => Navigator.of(context).pushNamed(LabelsScreen.routeName),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLabelTile(Label label) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: label.color,
        radius: 12,
      ),
      title: Text(label.name),
      onTap: () => _addLabel(label),
    );
  }

  void _addLabel(Label label) {
    setState(() {
      _selectedLabels.add(label);
    });
    widget.onLabelsChanged(_selectedLabels);

    // Add the relationship to the database if noteId is available
    if (widget.noteId != null) {
      Provider.of<LabelsProvider>(context, listen: false)
          .addLabelToNote(widget.noteId!, label.id!);
    }
  }

  void _removeLabel(Label label) {
    setState(() {
      _selectedLabels.removeWhere((selected) => selected.id == label.id);
    });
    widget.onLabelsChanged(_selectedLabels);

    // Remove the relationship from the database if noteId is available
    if (widget.noteId != null) {
      Provider.of<LabelsProvider>(context, listen: false)
          .removeLabelFromNote(widget.noteId!, label.id!);
    }
  }
} 