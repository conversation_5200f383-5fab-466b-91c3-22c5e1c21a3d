import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill_extensions/flutter_quill_extensions.dart';
import '../widgets/fullscreen_image_viewer.dart';

/// Custom embed builders for the notes app
class CustomEmbedBuilders {
  /// Get custom embed builders for the editor
  static List<EmbedBuilder> get customEditorBuilders {
    // Get default builders
    final defaultBuilders = FlutterQuillEmbeds.editorBuilders();
    
    // Add our custom image builder at the beginning
    return [
      CustomImageEmbedBuilder(),
      ...defaultBuilders,
    ];
  }

  /// Get custom embed builders for web editor
  static List<EmbedBuilder> get customWebEditorBuilders {
    // Get default web builders
    final defaultWebBuilders = FlutterQuillEmbeds.editorWebBuilders();
    
    // Add our custom image builder at the beginning
    return [
      CustomImageEmbedBuilder(),
      ...defaultWebBuilders,
    ];
  }
}

/// Custom image embed builder that adds border radius to images
class CustomImageEmbedBuilder implements EmbedBuilder {
  @override
  String get key => 'image';
  
  @override
  bool get expanded => false;

  @override
  Widget build(BuildContext context, EmbedContext embedContext) {
    final node = embedContext.node;
    final imageUrl = node.value.data;
    
    // First we determine if this is a network image or a local image
    final Widget image = imageUrl.startsWith('http')
        ? _buildNetworkImage(imageUrl)
        : _buildLocalImage(imageUrl);

    // Wrap in a GestureDetector to handle taps for fullscreen view
    return Container(
      // Add margin to ensure spacing between images
      margin: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 8.0),
      child: GestureDetector(
        onTap: () => _showFullScreenImage(context, imageUrl),
        child: Hero(
          tag: imageUrl,
          child: ClipRRect(
            // Add border radius of 14
            borderRadius: BorderRadius.circular(14),
            child: image,
          ),
        ),
      ),
    );
  }
  
  // Show fullscreen image view with delete option
  void _showFullScreenImage(BuildContext context, String imageUrl) {
    // Try to find the editor controller through the context
    QuillController? controller;
    
    try {
      // Look up the widget tree for the QuillController
      final editor = context.findAncestorWidgetOfExactType<QuillEditor>();
      if (editor != null) {
        controller = editor.controller;
      }
    } catch (e) {
      // Ignore errors in finding controller
    }
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return FullscreenImageViewer(
          imageUrl: imageUrl,
          controller: controller, // Pass controller if found
        );
      },
    );
  }
  

  
  @override
  WidgetSpan buildWidgetSpan(Widget child) {
    return WidgetSpan(
      alignment: PlaceholderAlignment.middle,
      child: child,
    );
  }
  
  @override
  String toPlainText(Embed node) {
    return '[Image]';
  }

  Widget _buildNetworkImage(String url) {
    return LayoutBuilder(builder: (context, constraints) {
      // Calculate the width as 50% of the available width
      // but with a minimum width to prevent too small images
      final width = max(constraints.maxWidth * 0.5, 120.0);
      
      return SizedBox(
        width: width,
        child: Image.network(
          url,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return _buildErrorImage();
          },
        ),
      );
    });
  }

  Widget _buildLocalImage(String path) {
    try {
      return LayoutBuilder(builder: (context, constraints) {
        // Calculate the width as 50% of the available width
        // but with a minimum width to prevent too small images
        final width = max(constraints.maxWidth * 0.5, 120.0);
        
        return SizedBox(
          width: width,
          child: Image.file(
            File(path),
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return _buildErrorImage();
            },
          ),
        );
      });
    } catch (e) {
      return _buildErrorImage();
    }
  }

  Widget _buildErrorImage() {
    return Container(
      width: 200,
      height: 200,
      color: Colors.grey[300],
      child: const Center(
        child: Icon(
          Icons.image_not_supported,
          size: 40,
          color: Colors.grey,
        ),
      ),
    );
  }
}
