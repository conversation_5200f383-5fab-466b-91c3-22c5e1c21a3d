import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider with ChangeNotifier {
  static const String _themePreferenceKey = 'theme_mode';
  ThemeMode _themeMode = ThemeMode.dark; // Default to dark mode

  ThemeMode get themeMode => _themeMode;

  ThemeProvider() {
    _loadThemePreference();
  }

  // Load theme preference from SharedPreferences
  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final themeModeIndex = prefs.getInt(_themePreferenceKey);

    if (themeModeIndex != null) {
      _themeMode = ThemeMode.values[themeModeIndex];
      notifyListeners();
    }
  }

  // Toggle between dark and light themes
  Future<void> toggleTheme() async {
    _themeMode =
        _themeMode == ThemeMode.dark ? ThemeMode.light : ThemeMode.dark;

    // Notify listeners immediately for instant UI update
    notifyListeners();

    // Save to preferences asynchronously without blocking UI
    _saveThemePreference();
  }

  // Private method to save theme preference asynchronously
  Future<void> _saveThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themePreferenceKey, _themeMode.index);
    } catch (e) {
      // Handle error silently or log it
      debugPrint('Failed to save theme preference: $e');
    }
  }

  // Set a specific theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;

    // Notify listeners immediately for instant UI update
    notifyListeners();

    // Save to preferences asynchronously without blocking UI
    _saveThemePreference();
  }

  // Check if currently in dark mode
  bool get isDarkMode => _themeMode == ThemeMode.dark;
}
