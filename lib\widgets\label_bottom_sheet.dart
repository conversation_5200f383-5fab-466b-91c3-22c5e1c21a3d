import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/label.dart';
import '../providers/labels_provider.dart';
import '../utils/constants.dart';

class LabelBottomSheet extends StatefulWidget {
  final int? noteId;
  final List<Label> selectedLabels;
  final Function(List<Label>) onLabelsChanged;

  const LabelBottomSheet({
    Key? key,
    required this.noteId,
    required this.selectedLabels,
    required this.onLabelsChanged,
  }) : super(key: key);

  static void show(
    BuildContext context, {
    required int? noteId,
    required List<Label> selectedLabels,
    required Function(List<Label>) onLabelsChanged,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (ctx) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(ctx).viewInsets.bottom,
        ),
        child: LabelBottomSheet(
          noteId: noteId,
          selectedLabels: selectedLabels,
          onLabelsChanged: onLabelsChanged,
        ),
      ),
    );
  }

  @override
  State<LabelBottomSheet> createState() => _LabelBottomSheetState();
}

class _LabelBottomSheetState extends State<LabelBottomSheet> {
  late List<Label> _selectedLabels;
  final _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _selectedLabels = List.from(widget.selectedLabels);
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                hintText: 'Search or add a tag',
                prefixText: _searchQuery.isEmpty ? '' : null,
                prefixIcon: _searchQuery.isEmpty 
                    ? const Icon(Icons.search) 
                    : null,
                suffixIcon: const Icon(Icons.search),
                filled: true,
                fillColor: colorScheme.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: colorScheme.onBackground.withOpacity(0.1),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: colorScheme.primary,
                  ),
                ),
              ),
              autofocus: true,
            ),
          ),
          Flexible(
            child: Consumer<LabelsProvider>(
              builder: (context, labelsProvider, child) {
                if (labelsProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final allLabels = labelsProvider.labels;
                final filteredLabels = _searchQuery.isEmpty
                    ? allLabels
                    : allLabels.where((label) => 
                        label.name.toLowerCase().contains(_searchQuery)).toList();
                
                return CustomScrollView(
                  shrinkWrap: true,
                  slivers: [
                    // Selected labels section
                    if (_selectedLabels.isNotEmpty)
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final label = _selectedLabels[index];
                            if (_searchQuery.isNotEmpty && 
                                !label.name.toLowerCase().contains(_searchQuery)) {
                              return const SizedBox.shrink();
                            }
                            return Align(
                              alignment: Alignment.centerLeft,
                              child: _buildLabelTile(
                                label: label, 
                                isSelected: true,
                              ),
                            );
                          },
                          childCount: _selectedLabels.length,
                        ),
                      ),
                    
                    // Add new label button if search query doesn't match existing labels
                    if (_searchQuery.isNotEmpty && 
                        !allLabels.any((label) => 
                            label.name.toLowerCase() == _searchQuery.toLowerCase()))
                      SliverToBoxAdapter(
                        child: _buildAddNewLabelTile(),
                      ),
                    
                    // Available labels section
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final label = filteredLabels[index];
                          final isSelected = _selectedLabels.any(
                              (selected) => selected.id == label.id);
                          
                          if (isSelected) return const SizedBox.shrink();
                          
                          return _buildLabelTile(
                            label: label, 
                            isSelected: false,
                          );
                        },
                        childCount: filteredLabels.length,
                      ),
                    ),
                    
                    // No labels message
                    if (filteredLabels.isEmpty && _searchQuery.isEmpty)
                      const SliverToBoxAdapter(
                        child: Center(
                          child: Padding(
                            padding: EdgeInsets.symmetric(vertical: 40),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '#',
                                  style: TextStyle(
                                    fontSize: 50,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'No tags',
                                  style: TextStyle(
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLabelTile({required Label label, required bool isSelected}) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: label.color,
        radius: 14,
        child: isSelected 
            ? const Icon(Icons.check, color: Colors.white, size: 16) 
            : null,
      ),
      title: Text(
        '#${label.name}',
        style: TextStyle(
          color: isSelected 
              ? Theme.of(context).colorScheme.primary 
              : null,
        ),
      ),
      onTap: () {
        if (isSelected) {
          _removeLabel(label);
        } else {
          _addLabel(label);
        }
      },
    );
  }

  Widget _buildAddNewLabelTile() {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.grey.withOpacity(0.2),
        radius: 14,
        child: const Icon(Icons.add, size: 18),
      ),
      title: Text('Add "#${_searchQuery}"'),
      trailing: const Icon(Icons.add),
      onTap: () => _createNewLabel(_searchQuery),
    );
  }

  void _createNewLabel(String name) {
    if (name.isEmpty) return;
    
    final labelsProvider = Provider.of<LabelsProvider>(context, listen: false);
    final newLabel = Label(
      name: name,
      color: AppColors.noteColors[name.length % AppColors.noteColors.length],
    );
    
    labelsProvider.addLabel(newLabel).then((id) {
      if (id != -1) {
        final labelWithId = Label(
          id: id,
          name: newLabel.name,
          color: newLabel.color,
        );
        _addLabel(labelWithId);
        
        // Clear search after adding
        _searchController.clear();
      }
    });
  }

  void _addLabel(Label label) {
    if (_selectedLabels.any((selected) => selected.id == label.id)) return;
    
    setState(() {
      _selectedLabels.add(label);
    });
    widget.onLabelsChanged(_selectedLabels);

    // Add the relationship to the database if noteId is available
    if (widget.noteId != null) {
      Provider.of<LabelsProvider>(context, listen: false)
          .addLabelToNote(widget.noteId!, label.id!);
    }
  }

  void _removeLabel(Label label) {
    setState(() {
      _selectedLabels.removeWhere((selected) => selected.id == label.id);
    });
    widget.onLabelsChanged(_selectedLabels);

    // Remove the relationship from the database if noteId is available
    if (widget.noteId != null) {
      Provider.of<LabelsProvider>(context, listen: false)
          .removeLabelFromNote(widget.noteId!, label.id!);
    }
  }
} 