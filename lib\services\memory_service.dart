import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import '../models/note.dart';

class MemoryService {
  static final MemoryService _instance = MemoryService._internal();
  
  factory MemoryService() => _instance;
  
  MemoryService._internal();
  
  // Cache configuration
  final int _maxCachedNotes = 50;
  final Duration _cacheExpirationTime = const Duration(minutes: 30);
  
  // Note cache
  final Map<int, _CacheEntry<Note>> _noteCache = {};
  
  // Image cache controller
  final PaintingBinding _paintingBinding = PaintingBinding.instance;
  
  // Timer for periodic cache cleanup
  Timer? _cacheCleanupTimer;
  
  // Initialize the memory service
  void initialize() {
    // Start periodic cache cleanup
    _cacheCleanupTimer = Timer.periodic(
      const Duration(minutes: 15),
      (_) => cleanupCache(),
    );
  }
  
  // Dispose resources
  void dispose() {
    _cacheCleanupTimer?.cancel();
    clearCache();
  }
  
  // Add or update a note in the cache
  void cacheNote(Note note) {
    if (note.id == null) return;
    
    _noteCache[note.id!] = _CacheEntry(
      data: note,
      timestamp: DateTime.now(),
    );
    
    // Cleanup if cache exceeds max size
    if (_noteCache.length > _maxCachedNotes) {
      _removeOldestEntries(_noteCache.length - _maxCachedNotes);
    }
  }
  
  // Get a note from cache
  Note? getCachedNote(int id) {
    final entry = _noteCache[id];
    if (entry == null) return null;
    
    // Check if cache entry is expired
    if (DateTime.now().difference(entry.timestamp) > _cacheExpirationTime) {
      _noteCache.remove(id);
      return null;
    }
    
    // Update timestamp to keep it fresh
    _noteCache[id] = _CacheEntry(
      data: entry.data,
      timestamp: DateTime.now(),
    );
    
    return entry.data;
  }
  
  // Remove a note from cache
  void removeCachedNote(int id) {
    _noteCache.remove(id);
  }
  
  // Clear the entire note cache
  void clearCache() {
    if (kDebugMode) {
      print('Clearing note cache: ${_noteCache.length} items');
    }
    _noteCache.clear();
  }
  
  // Clean up expired cache entries
  void cleanupCache() {
    final now = DateTime.now();
    final beforeCount = _noteCache.length;
    
    _noteCache.removeWhere((_, entry) => 
      now.difference(entry.timestamp) > _cacheExpirationTime);
      
    final afterCount = _noteCache.length;
    if (beforeCount != afterCount && kDebugMode) {
      print('Cache cleanup: removed ${beforeCount - afterCount} expired items');
    }
  }
  
  // Remove oldest entries from cache
  void _removeOldestEntries(int count) {
    if (count <= 0) return;
    
    final entries = _noteCache.entries.toList()
      ..sort((a, b) => a.value.timestamp.compareTo(b.value.timestamp));
    
    for (int i = 0; i < count && i < entries.length; i++) {
      _noteCache.remove(entries[i].key);
    }
  }
  
  // Clear image cache
  void clearImageCache() {
    if (kDebugMode) {
      print('Clearing image cache');
    }
    _paintingBinding.imageCache.clear();
    _paintingBinding.imageCache.clearLiveImages();
  }
}

// Helper class for cache entries
class _CacheEntry<T> {
  final T data;
  final DateTime timestamp;
  
  _CacheEntry({
    required this.data,
    required this.timestamp,
  });
} 