import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'dart:async';

/// A handler to detect image URLs in the editor and convert them to embedded images
class UrlToImageHandler {
  static const List<String> _imageExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'
  ];
  
  /// Check if a URL points to an image based on extension or image hosting patterns
  static bool isImageUrl(String url) {
    final String lowerCaseUrl = url.toLowerCase();
    
    // Check common image extensions
    for (final ext in _imageExtensions) {
      if (lowerCaseUrl.endsWith(ext)) {
        return true;
      }
    }
    
    // Check known image hosting patterns
    if (lowerCaseUrl.contains('imgur.com/') ||
        lowerCaseUrl.contains('i.imgur.com/') ||
        lowerCaseUrl.contains('.googleusercontent.com/') ||
        lowerCaseUrl.contains('images.unsplash.com/') ||
        lowerCaseUrl.contains('media.giphy.com/') ||
        (lowerCaseUrl.contains('pbs.twimg.com/') && lowerCaseUrl.contains('/media/')) ||
        (lowerCaseUrl.contains('cloudinary.com/') && lowerCaseUrl.contains('/image/')) ||
        (lowerCaseUrl.contains('res.cloudinary.com'))) {
      return true;
    }
    
    return false;
  }
  
  /// Attach a listener to a QuillController to detect and convert image URLs
  static void attachToController(QuillController controller) {
    // Store previous content to compare changes
    String previousContent = '';
    
    // Current timeout to prevent multiple rapid conversions
    Timer? _debounceTimer;
    
    // Listen to document changes
    controller.document.changes.listen((_) {
      if (_debounceTimer?.isActive ?? false) {
        return;
      }
      
      // Debounce to avoid multiple conversions during rapid typing
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        final currentContent = controller.document.toPlainText();
        
        // Skip if content is the same as previous check
        if (currentContent == previousContent) {
          return;
        }
        
        previousContent = currentContent;
        
        // Check for URLs in the text
        final RegExp urlRegex = RegExp(
          r'https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)',
          caseSensitive: false,
          multiLine: true,
        );
        
        final matches = urlRegex.allMatches(currentContent);
        
        // Process matches from end to start to avoid offset issues
        for (final match in matches.toList().reversed) {
          final url = match.group(0)!;
          
          // If the URL is an image URL, convert it to an embedded image
          if (isImageUrl(url)) {
            final start = controller.document.toPlainText().indexOf(url);
            if (start >= 0) {
              // Replace the URL with an embedded image
              controller.replaceText(
                start, 
                url.length, 
                '', 
                null,
              );
              
              // Insert the image at the same position
              controller.updateSelection(
                TextSelection.collapsed(offset: start),
                ChangeSource.local,
              );
              
              // Use custom insert image
              _insertImage(controller, url);
            }
          }
        }
      });
    });
  }
  
  /// Insert an image into the document at the current selection
  static void _insertImage(QuillController controller, String url) {
    final index = controller.selection.baseOffset;
    
    // Create embed
    final embed = BlockEmbed.image(url);
    
    // Insert image embed
    controller.document.insert(index, embed);
    
    // Move cursor after image
    controller.updateSelection(
      TextSelection.collapsed(offset: index + 1),
      ChangeSource.local,
    );
  }
}
