import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:convert';

import '../models/label.dart';
import '../models/note.dart';
import '../providers/labels_provider.dart';
import '../widgets/notes_staggered_grid.dart';

class LabelFilterScreen extends StatefulWidget {
  final int labelId;

  const LabelFilterScreen({Key? key, required this.labelId}) : super(key: key);

  @override
  _LabelFilterScreenState createState() => _LabelFilterScreenState();
}

class _LabelFilterScreenState extends State<LabelFilterScreen> {
  List<Note> _filteredNotes = [];
  bool _isLoading = true;
  Label? _currentLabel;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadNotes();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadNotes() async {
    setState(() {
      _isLoading = true;
    });

    final labelsProvider = Provider.of<LabelsProvider>(context, listen: false);
    _currentLabel = labelsProvider.getLabelById(widget.labelId);
    
    if (_currentLabel != null) {
      _filteredNotes = await labelsProvider.getNotesWithLabel(widget.labelId);
      
      // If search query is active, filter notes further
      if (_searchQuery.isNotEmpty) {
        _filteredNotes = _filteredNotes.where((note) {
          final titleLower = note.title.toLowerCase();
          final contentText = _getPlainTextFromDelta(note.content);
          
          return titleLower.contains(_searchQuery.toLowerCase()) ||
              contentText.toLowerCase().contains(_searchQuery.toLowerCase());
        }).toList();
      }
    }

    setState(() {
      _isLoading = false;
    });
  }

  String _getPlainTextFromDelta(String jsonContent) {
    try {
      // Parse the JSON string into a Dart object
      final List<dynamic> deltaJson = jsonDecode(jsonContent);

      // Extract text content from the delta operations
      String plainText = '';
      for (final op in deltaJson) {
        if (op['insert'] is String) {
          plainText += op['insert'];
        } else if (op['insert'] is Map) {
          // For embedded objects, add a placeholder
          plainText += '[attachment]';
        }
      }

      return plainText.trim();
    } catch (e) {
      // If JSON parsing fails, return empty string
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _currentLabel != null 
          ? Row(
              children: [
                Icon(
                  Icons.label_outline,
                  color: _currentLabel!.color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(_currentLabel!.name),
              ],
            )
          : const Text('Label'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearchBar(context);
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _filteredNotes.isEmpty
              ? Center(
                  child: Text(
                    'No notes with this label${_searchQuery.isNotEmpty ? ' matching "$_searchQuery"' : ''}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                )
              : NotesStaggeredGrid(
                  notes: _filteredNotes,
                  onNotePressed: (note) => _openNoteDetail(note),
                ),
    );
  }

  void _showSearchBar(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search notes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Enter search term',
                prefixIcon: Icon(Icons.search),
              ),
              autofocus: true,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _loadNotes();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchQuery = '';
              });
              _loadNotes();
              Navigator.pop(context);
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _openNoteDetail(Note note) {
    Navigator.pushNamed(
      context,
      '/note-detail',
      arguments: {'noteId': note.id},
    ).then((_) {
      // Refresh notes when returning from detail screen
      _loadNotes();
    });
  }
}
