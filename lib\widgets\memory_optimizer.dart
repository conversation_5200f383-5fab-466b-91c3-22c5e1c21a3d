import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/memory_service.dart';

class MemoryOptimizer extends StatefulWidget {
  final Widget child;
  final bool enableAutomaticMemoryManagement;
  final MemoryService memoryService;
  
  const MemoryOptimizer({
    Key? key,
    required this.child,
    this.enableAutomaticMemoryManagement = true,
    required this.memoryService,
  }) : super(key: key);

  @override
  State<MemoryOptimizer> createState() => _MemoryOptimizerState();
}

class _MemoryOptimizerState extends State<MemoryOptimizer> with WidgetsBindingObserver {
  late final MemoryService _memoryService;

  @override
  void initState() {
    super.initState();
    _memoryService = widget.memoryService;
    
    // Register for app lifecycle events
    if (widget.enableAutomaticMemoryManagement) {
      WidgetsBinding.instance.addObserver(this);
    }
  }

  @override
  void dispose() {
    if (widget.enableAutomaticMemoryManagement) {
      WidgetsBinding.instance.removeObserver(this);
    }
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Optimize memory based on app lifecycle state
    switch (state) {
      case AppLifecycleState.paused:
        // App is not visible, but still running in background
        // Clean up some resources to free memory
        _memoryService.cleanupCache();
        break;
        
      case AppLifecycleState.inactive:
        // App is in an inactive state (like when receiving a phone call)
        // Minimal cleanup
        break;
        
      case AppLifecycleState.detached:
        // App is in the background and detached
        // Aggressive cleanup
        _memoryService.clearCache();
        _memoryService.clearImageCache();
        break;
        
      case AppLifecycleState.resumed:
        // App is visible and running
        // No cleanup needed
        break;
        
      case AppLifecycleState.hidden:
        // App is hidden but still running
        // Clean up image cache
        _memoryService.clearImageCache();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

// Extension to provide memory optimization methods throughout the app
extension MemoryOptimizerExtension on BuildContext {
  MemoryService get memoryService => Provider.of<MemoryService>(this, listen: false);
  
  void optimizeMemory() {
    memoryService.cleanupCache();
  }
  
  void clearMemory() {
    memoryService.clearCache();
    memoryService.clearImageCache();
  }
} 