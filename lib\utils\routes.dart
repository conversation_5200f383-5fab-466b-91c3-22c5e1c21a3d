import 'package:flutter/material.dart';
import '../screens/labels_screen.dart';
import '../screens/label_notes_screen.dart';
import '../models/label.dart';

class AppRoutes {
  static const String labels = '/labels';
  static const String labelNotes = '/label-notes';

  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case labels:
        return MaterialPageRoute(
          builder: (_) => const LabelsScreen(),
        );
      case labelNotes:
        final args = settings.arguments as LabelNotesArguments;
        return MaterialPageRoute(
          builder: (_) => LabelNotesScreen(label: args.label),
        );
      default:
        return null;
    }
  }
}

class LabelNotesArguments {
  final Label label;

  LabelNotesArguments({required this.label});
} 