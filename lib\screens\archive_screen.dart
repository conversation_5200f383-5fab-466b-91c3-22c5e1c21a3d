import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:convert';
import '../providers/notes_provider.dart';
import '../providers/app_lock_provider.dart';
import '../providers/labels_provider.dart';
import '../models/note.dart';
import '../models/label.dart';
import '../utils/constants.dart';
import '../utils/date_formatter.dart';
import '../widgets/pin_screen.dart';
import '../widgets/biometric_guidance_dialog.dart';
import '../widgets/shared_note_card.dart';
import '../services/auth_service.dart';
import 'note_detail_screen.dart';
// flutter_quill import removed as it's not needed

class ArchiveScreen extends StatefulWidget {
  const ArchiveScreen({super.key});

  @override
  State<ArchiveScreen> createState() => _ArchiveScreenState();
}

class _ArchiveScreenState extends State<ArchiveScreen> {
  bool _isGridView = true;
  bool _isAuthenticating = true;
  bool _isBiometricAuthInProgress = false;
  
  // Multi-select mode state
  bool _isMultiSelectMode = false;
  final Set<int> _selectedNoteIds = {};

  @override
  void initState() {
    super.initState();
    // Check if authentication is required for archive access
    _checkArchiveAuthentication();
  }

  Future<void> _checkArchiveAuthentication() async {
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    
    // Check if we need to authenticate for archive access
    final authRequired = await appLockProvider.isArchiveAuthenticationRequired();
    
    if (!authRequired) {
      // No authentication required, proceed to load notes
      setState(() {
        _isAuthenticating = false;
      });
      Provider.of<NotesProvider>(context, listen: false).fetchNotes();
    } else {
      // Authentication required, stay in authentication state
      setState(() {
        _isAuthenticating = true;
      });
      
      // Attempt biometric authentication if enabled
      if (appLockProvider.isBiometricEnabled) {
        _authenticateWithBiometrics();
      }
    }
  }
  
  // Handle PIN authentication
  void _handlePinEntered(String pin) async {
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    
    // Different methods depending on whether using app PIN or separate archive PIN
    final bool result = await appLockProvider.authenticateArchiveWithPin(pin);
    
    if (result) {
      setState(() {
        _isAuthenticating = false;
      });
      // Load notes after successful authentication
      Provider.of<NotesProvider>(context, listen: false).fetchNotes();
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Incorrect PIN. Please try again.'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.only(bottom: 16.0, left: 16.0, right: 16.0),
        ),
      );
    }
  }
  
  // Authenticate using biometrics
  Future<void> _authenticateWithBiometrics() async {
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    
    if (!appLockProvider.isBiometricEnabled) return;
    
    setState(() {
      _isBiometricAuthInProgress = true;
    });
    
    try {
      // Attempt biometric authentication
      final success = await appLockProvider.authenticateArchiveWithBiometrics();
      
      if (success && mounted) {
        setState(() {
          _isAuthenticating = false;
          _isBiometricAuthInProgress = false;
        });
        // Load notes after successful authentication
        Provider.of<NotesProvider>(context, listen: false).fetchNotes();
      } else if (mounted) {
        setState(() {
          _isBiometricAuthInProgress = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isBiometricAuthInProgress = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Biometric authentication failed'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // Extract plain text from the Quill Delta JSON
  String _getPlainTextFromDelta(String jsonContent) {
    try {
      // Parse the JSON string into a Dart object
      final List<dynamic> deltaJson = jsonDecode(jsonContent);

      // Extract text content from the delta operations
      String plainText = '';
      for (final op in deltaJson) {
        if (op['insert'] is String) {
          plainText += op['insert'];
        } else if (op['insert'] is Map) {
          // For embedded objects, add a placeholder
          plainText += '[attachment]';
        }
      }

      return plainText.trim();
    } catch (e) {
      // If JSON parsing fails, return the content as-is
      return jsonContent;
    }
  }

  // Toggle selection of a note
  void _toggleNoteSelection(int noteId) {
    setState(() {
      if (_selectedNoteIds.contains(noteId)) {
        _selectedNoteIds.remove(noteId);
        // Exit multi-select mode if no notes are selected
        if (_selectedNoteIds.isEmpty) {
          _isMultiSelectMode = false;
        }
      } else {
        _selectedNoteIds.add(noteId);
      }
    });
  }

  // Enter multi-select mode with initial note selected
  void _enterMultiSelectMode(int initialNoteId) {
    setState(() {
      _isMultiSelectMode = true;
      _selectedNoteIds.add(initialNoteId);
    });
  }

  // Exit multi-select mode
  void _exitMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = false;
      _selectedNoteIds.clear();
    });
  }

  // Unarchive selected notes
  void _unarchiveSelectedNotes() async {
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    
    for (final noteId in _selectedNoteIds) {
      await notesProvider.unarchiveNote(noteId);
    }
    
    _exitMultiSelectMode();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${_selectedNoteIds.length} notes unarchived'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  // Show color picker for selected notes
  void _showColorPicker() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    // Define color options
    final List<Color> colorOptions = [
      Colors.white,
      Colors.red.shade200,
      Colors.orange.shade200,
      Colors.yellow.shade200,
      Colors.green.shade200,
      Colors.blue.shade200,
      Colors.purple.shade200,
      Colors.pink.shade200,
      Colors.brown.shade200,
      Colors.grey.shade300,
    ];
    
    if (isDarkMode) {
      // Darker variants for dark mode
      colorOptions.replaceRange(0, colorOptions.length, [
        const Color(0xFF2D2D2D), // Dark grey (default)
        Colors.red.shade900,
        Colors.orange.shade900,
        Colors.amber.shade900,
        Colors.green.shade900,
        Colors.blue.shade900,
        Colors.purple.shade900,
        Colors.pink.shade900,
        Colors.brown.shade900,
        Colors.grey.shade800,
      ]);
    }
    
    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0, bottom: 16.0),
              child: Text(
                'Note color',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 5,
                mainAxisSpacing: 8,
                crossAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              itemCount: colorOptions.length,
              itemBuilder: (context, index) {
                return InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    _applyColorToSelectedNotes(colorOptions[index]);
                  },
                  borderRadius: BorderRadius.circular(50),
                  child: Container(
                    decoration: BoxDecoration(
                      color: colorOptions[index],
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isDarkMode ? Colors.white30 : Colors.black12,
                        width: 1,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  // Apply color to selected notes
  void _applyColorToSelectedNotes(Color color) async {
    // Convert color to string format that can be stored in the database
    final colorValue = color.value.toString();
    
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    await notesProvider.updateNotesColor(_selectedNoteIds, colorValue);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Color applied to ${_selectedNoteIds.length} notes'),
        duration: const Duration(seconds: 2),
      ),
    );
    
    _exitMultiSelectMode();
  }
  
  // Handle more menu actions
  void _handleMoreMenuAction(String action) {
    switch (action) {
      case 'labels':
        _showLabelsDialog();
        break;
      case 'delete':
        _deleteSelectedNotes();
        break;
    }
  }
  
  // Show labels dialog
  void _showLabelsDialog() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => Column(
          children: [
            // Handle and title
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Add label',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
            ),
            
            // Label list
            Expanded(
              child: Consumer<LabelsProvider>(
                builder: (context, labelsProvider, _) {
                  if (labelsProvider.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  
                  if (labelsProvider.labels.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.label_outline,
                            size: 56,
                            color: isDarkMode ? Colors.white38 : Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No labels yet',
                            style: TextStyle(
                              fontSize: 16,
                              color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.add),
                            label: const Text('Create new label'),
                            onPressed: () {
                              // TODO: Implement create new label functionality
                              Navigator.pop(context);
                            },
                          ),
                        ],
                      ),
                    );
                  }
                  
                  return ListView.builder(
                    controller: scrollController,
                    itemCount: labelsProvider.labels.length + 1, // +1 for "Create new label" option
                    itemBuilder: (context, index) {
                      if (index == labelsProvider.labels.length) {
                        // "Create new label" option at the end
                        return ListTile(
                          leading: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(Icons.add, size: 18),
                          ),
                          title: const Text('Create new label'),
                          onTap: () {
                            // TODO: Implement create new label functionality
                            Navigator.pop(context);
                          },
                        );
                      }
                      
                      final label = labelsProvider.labels[index];
                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: label.color,
                          radius: 12,
                        ),
                        title: Text(label.name),
                        onTap: () {
                          _addLabelToSelectedNotes(label.id!);
                          Navigator.pop(context);
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Add label to selected notes
  void _addLabelToSelectedNotes(int labelId) async {
    final labelsProvider = Provider.of<LabelsProvider>(context, listen: false);
    await labelsProvider.addLabelToMultipleNotes(labelId, _selectedNoteIds);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Label added to ${_selectedNoteIds.length} notes'),
        duration: const Duration(seconds: 2),
      ),
    );
    
    _exitMultiSelectMode();
  }
  
  // Delete selected notes
  void _deleteSelectedNotes() async {
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    final count = _selectedNoteIds.length;
    
    for (final noteId in _selectedNoteIds) {
      await notesProvider.moveToTrash(noteId);
    }
    
    _exitMultiSelectMode();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$count notes moved to trash'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // If authentication is required, show authentication screen
    if (_isAuthenticating) {
      return Consumer<AppLockProvider>(
        builder: (context, appLockProvider, _) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Archive'),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  // Set archive authentication flag to true before returning to home
                  appLockProvider.forceArchiveAuthentication();
                  Navigator.of(context).pop();
                },
              ),
            ),
            body: FutureBuilder<bool>(
              future: appLockProvider.isBiometricEnrollmentNeeded(),
              initialData: false,
              builder: (context, enrollmentSnapshot) {
                final needsEnrollment = enrollmentSnapshot.data ?? false;
                
                if (needsEnrollment && appLockProvider.isBiometricEnabled) {
                  // Show enrollment guidance if needed
                  Future.microtask(() {
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (context) => BiometricGuidanceDialog(
                        onDismiss: () async {
                          final newStatus = await appLockProvider.authService.getBiometricStatus();
                          if (newStatus == BiometricStatus.enrolled) {
                            await appLockProvider.silentlyUpdateBiometricEnrollment();
                            if (mounted) setState(() {});
                          }
                        },
                      ),
                    );
                  });
                }
                
                return PinScreen(
                  title: 'Unlock Archive',
                  subtitle: 'Enter your PIN to access archived notes',
                  pinLength: 4,
                  onPinSubmitted: _handlePinEntered,
                  showBiometrics: appLockProvider.isBiometricEnabled && 
                                  !_isBiometricAuthInProgress && 
                                  !needsEnrollment,
                  onBiometricPressed: (appLockProvider.isBiometricEnabled && !needsEnrollment)
                    ? _authenticateWithBiometrics
                    : null,
                );
              }
            ),
          );
        }
      );
    }

    // If authenticated, show archive content
    return Scaffold(
      appBar: _isMultiSelectMode ? _buildMultiSelectAppBar() : _buildNormalAppBar(),
      body: Consumer<NotesProvider>(
        builder: (context, notesProvider, child) {
          if (notesProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (notesProvider.archivedNotes.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.archive_outlined,
                    size: 80,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Archive is empty',
                    style: AppTextStyles.heading2,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Notes you archive will appear here',
                    style: AppTextStyles.caption,
                  ),
                ],
              ),
            );
          }

          // Choose the appropriate view based on state
          return _isGridView
              ? _buildGridView(notesProvider.archivedNotes)
              : _buildListView(notesProvider.archivedNotes);
        },
      ),
    );
  }
  
  PreferredSizeWidget _buildNormalAppBar() {
    return AppBar(
      title: const Text('Archive'),
      centerTitle: false,
      elevation: 0,
      actions: [
        // View toggle button
        IconButton(
          icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
          tooltip:
              _isGridView ? 'Switch to list view' : 'Switch to grid view',
          onPressed: () {
            setState(() {
              _isGridView = !_isGridView;
            });
          },
        ),
      ],
    );
  }

  PreferredSizeWidget _buildMultiSelectAppBar() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return AppBar(
      backgroundColor: isDarkMode ? Colors.indigo.shade900 : Colors.indigo.shade700,
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: _exitMultiSelectMode,
      ),
      title: Text('${_selectedNoteIds.length} selected'),
      elevation: 2,
      actions: [
        // Unarchive
        IconButton(
          icon: const Icon(Icons.unarchive_outlined),
          tooltip: 'Unarchive',
          onPressed: _unarchiveSelectedNotes,
        ),
        
        // Change color
        IconButton(
          icon: const Icon(Icons.palette_outlined),
          tooltip: 'Change color',
          onPressed: () => _showColorPicker(),
        ),
        
        // More options
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: _handleMoreMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'labels',
              child: Row(
                children: [
                  Icon(Icons.label_outline, size: 20),
                  SizedBox(width: 8),
                  Text('Add labels'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_outline, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Grid view layout for notes
  Widget _buildGridView(List<Note> notes) {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 0.8,
      ),
      itemCount: notes.length,
      itemBuilder: (context, index) {
        final note = notes[index];
        return SharedNoteCard(
          note: note,
          isSelected: _selectedNoteIds.contains(note.id),
          isInSelectionMode: _isMultiSelectMode,
          showPinIcon: false, // Archive notes can't be pinned
          onLongPress: note.id != null ? () => _enterMultiSelectMode(note.id!) : null,
          onTap: () {
            if (_isMultiSelectMode) {
              if (note.id != null) {
                _toggleNoteSelection(note.id!);
              }
            } else {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => NoteDetailScreen(noteId: note.id),
                ),
              ).then((_) {
                // Refresh notes when coming back from detail screen
                Provider.of<NotesProvider>(context, listen: false).fetchNotes();
              });
            }
          },
        );
      },
    );
  }

  // List view layout for notes
  Widget _buildListView(List<Note> notes) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: notes.length,
      itemBuilder: (context, index) {
        final note = notes[index];
        return SharedNoteCard(
          note: note,
          isSelected: _selectedNoteIds.contains(note.id),
          isInSelectionMode: _isMultiSelectMode,
          showPinIcon: false, // Archive notes can't be pinned
          onLongPress: note.id != null ? () => _enterMultiSelectMode(note.id!) : null,
          onTap: () {
            if (_isMultiSelectMode) {
              if (note.id != null) {
                _toggleNoteSelection(note.id!);
              }
            } else {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => NoteDetailScreen(noteId: note.id),
                ),
              ).then((_) {
                // Refresh notes when coming back from detail screen
                Provider.of<NotesProvider>(context, listen: false).fetchNotes();
              });
            }
          },
        );
      },
    );
  }
}
