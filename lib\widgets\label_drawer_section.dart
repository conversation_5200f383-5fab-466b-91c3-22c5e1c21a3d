import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/label.dart';
import '../providers/labels_provider.dart';
import '../screens/label_filter_screen.dart';

class LabelDrawerSection extends StatelessWidget {
  const LabelDrawerSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<LabelsProvider>(
      builder: (context, labelsProvider, child) {
        final visibleLabels = labelsProvider.visibleLabels;
        
        if (visibleLabels.isEmpty) {
          return const SizedBox.shrink();
        }
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.only(left: 16.0, top: 16.0, bottom: 8.0),
              child: Text(
                'Labels',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ),
            ...visibleLabels.map((label) => _buildLabelTile(context, label, labelsProvider)),
            // Add label management tile
            ListTile(
              dense: true,
              leading: const Icon(Icons.add, size: 20),
              title: const Text('Edit labels'),
              onTap: () {
                Navigator.pop(context); // Close drawer
                _showLabelManagementDialog(context);
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildLabelTile(BuildContext context, Label label, LabelsProvider labelsProvider) {
    final labelCount = labelsProvider.getLabelCount(label.id!);
    final isSelected = labelsProvider.selectedLabelIds.contains(label.id);
    
    return ListTile(
      dense: true,
      selected: isSelected,
      leading: Icon(
        Icons.label_outline,
        size: 20,
        color: label.color,
      ),
      title: Text(label.name),
      trailing: Text(
        '$labelCount',
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      ),
      onTap: () {
        Navigator.pop(context); // Close drawer
        
        if (isSelected) {
          // If already selected, clear selection
          labelsProvider.clearSelectedLabels();
        } else {
          // Otherwise navigate to label filter screen
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => LabelFilterScreen(labelId: label.id!),
            ),
          );
        }
      },
    );
  }

  void _showLabelManagementDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _LabelManagementDialog(),
    );
  }
}

class _LabelManagementDialog extends StatefulWidget {
  @override
  _LabelManagementDialogState createState() => _LabelManagementDialogState();
}

class _LabelManagementDialogState extends State<_LabelManagementDialog> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit labels'),
      content: Consumer<LabelsProvider>(
        builder: (context, labelsProvider, child) {
          final allLabels = labelsProvider.labels;
          final filteredLabels = allLabels
              .where((label) => label.name.toLowerCase().contains(_searchQuery.toLowerCase()))
              .toList();
              
          return SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search labels',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: filteredLabels.length,
                    itemBuilder: (context, index) {
                      final label = filteredLabels[index];
                      return ListTile(
                        leading: Icon(
                          Icons.label_outline,
                          color: label.color,
                        ),
                        title: Text(label.name),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Toggle visibility
                            IconButton(
                              icon: Icon(
                                label.isVisible ? Icons.visibility : Icons.visibility_off,
                                color: label.isVisible ? null : Colors.grey,
                              ),
                              onPressed: () {
                                labelsProvider.toggleLabelVisibility(label.id!);
                              },
                            ),
                            // Edit button
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: () {
                                _showEditLabelDialog(context, label);
                              },
                            ),
                            // Delete button
                            IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () {
                                _showDeleteConfirmationDialog(context, label);
                              },
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Done'),
        ),
      ],
    );
  }

  void _showEditLabelDialog(BuildContext context, Label label) {
    final TextEditingController controller = TextEditingController(text: label.name);
    final Color initialColor = label.color;
    Color selectedColor = initialColor;

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Edit label'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Label name',
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            // Simple color picker
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                Colors.red,
                Colors.pink,
                Colors.purple,
                Colors.deepPurple,
                Colors.indigo,
                Colors.blue,
                Colors.lightBlue,
                Colors.cyan,
                Colors.teal,
                Colors.green,
                Colors.lightGreen,
                Colors.lime,
                Colors.yellow,
                Colors.amber,
                Colors.orange,
                Colors.deepOrange,
                Colors.brown,
                Colors.grey,
                Colors.blueGrey,
              ].map((color) => GestureDetector(
                onTap: () {
                  setState(() {
                    selectedColor = color;
                  });
                },
                child: Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: selectedColor == color ? Colors.white : Colors.transparent,
                      width: 2,
                    ),
                  ),
                ),
              )).toList(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                final updatedLabel = Label(
                  id: label.id,
                  name: controller.text.trim(),
                  color: selectedColor,
                  isVisible: label.isVisible,
                  createdAt: label.createdAt,
                  updatedAt: DateTime.now(),
                );
                
                Provider.of<LabelsProvider>(context, listen: false)
                    .updateLabel(updatedLabel);
                    
                Navigator.of(dialogContext).pop();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, Label label) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete label'),
        content: Text('Are you sure you want to delete "${label.name}"? This will remove the label from all notes.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<LabelsProvider>(context, listen: false)
                  .deleteLabel(label.id!);
                  
              Navigator.of(dialogContext).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
