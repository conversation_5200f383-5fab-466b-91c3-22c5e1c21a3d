import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/label.dart';
import '../models/note.dart';
import '../providers/labels_provider.dart';
import '../services/database_service.dart';
import '../widgets/shared_note_card.dart';

class LabelNotesScreen extends StatefulWidget {
  static const routeName = '/label-notes';
  final Label label;

  const LabelNotesScreen({Key? key, required this.label}) : super(key: key);

  @override
  State<LabelNotesScreen> createState() => _LabelNotesScreenState();
}

class _LabelNotesScreenState extends State<LabelNotesScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<Note> _notes = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNotes();
  }

  Future<void> _loadNotes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final notes = await _databaseService.getNotesWithLabel(widget.label.id!);
      
      setState(() {
        _notes = notes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading notes: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: widget.label.color,
              radius: 10,
            ),
            const SizedBox(width: 8),
            Text(widget.label.name),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNotes,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _notes.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.note_alt_outlined, size: 48, color: Colors.grey),
                      const SizedBox(height: 16),
                      Text(
                        'No notes with the "${widget.label.name}" label',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _notes.length,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemBuilder: (context, index) {
                    return SharedNoteCard(
                      note: _notes[index],
                      onLongPress: () => _showActionsDialog(_notes[index]),
                      actionButtons: [
                        IconButton(
                          icon: const Icon(Icons.label_off_outlined, size: 18),
                          onPressed: () => _removeLabel(_notes[index]),
                          tooltip: 'Remove label',
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                      ],
                    );
                  },
                ),
    );
  }

  void _showActionsDialog(Note note) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(note.title.isEmpty ? 'Untitled' : note.title),
        content: const Text('What would you like to do?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _removeLabel(note);
            },
            child: const Text('Remove Label'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _removeLabel(Note note) async {
    try {
      await Provider.of<LabelsProvider>(context, listen: false)
          .removeLabelFromNote(note.id!, widget.label.id!);
      
      await _loadNotes();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Removed "${widget.label.name}" label from note')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error removing label: $e')),
        );
      }
    }
  }
} 