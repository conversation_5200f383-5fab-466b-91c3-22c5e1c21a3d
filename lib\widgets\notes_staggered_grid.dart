import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../models/note.dart';
import '../utils/responsive_helper.dart';
import 'shared_note_card.dart';

class NotesStaggeredGrid extends StatelessWidget {
  final List<Note> notes;
  final Function(Note)? onNotePressed;
  final Function(Note)? onNoteLongPressed;
  final bool isInSelectionMode;
  final Set<int>? selectedNoteIds;

  const NotesStaggeredGrid({
    Key? key,
    required this.notes,
    this.onNotePressed,
    this.onNoteLongPressed,
    this.isInSelectionMode = false,
    this.selectedNoteIds,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine the cross axis count based on screen width
    final int crossAxisCount = ResponsiveHelper.getGridCrossAxisCount(context);
    
    // Calculate a more generous height estimate based on content
    final double baseHeight = 150.0; // Base height per note
    
    double totalEstimatedHeight = 0;
    
    // Calculate estimated height based on content
    for (int i = 0; i < notes.length; i++) {
      final note = notes[i];
      double noteHeight = baseHeight;
      
      // Add height for title
      if (note.title.isNotEmpty) {
        noteHeight += note.title.length * 0.5;
      }
      
      // Add height for content preview
      final contentLength = note.content.length;
      noteHeight += contentLength > 100 ? 100 : contentLength * 0.2;
      
      // Add to total
      totalEstimatedHeight += noteHeight;
    }
    
    // Calculate and distribute height
    final double estimatedHeight = totalEstimatedHeight / crossAxisCount;
    
    // Ensure minimum height
    final double finalHeight = estimatedHeight < 100 ? 
        notes.length * 150.0 : estimatedHeight;
    
    return Container(
      height: finalHeight,
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: AlignedGridView.count(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: 10,
        crossAxisSpacing: 10,
        itemCount: notes.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) {
          final note = notes[index];
          return SharedNoteCard(
            note: note,
            isSelected: selectedNoteIds?.contains(note.id) ?? false,
            isInSelectionMode: isInSelectionMode,
            onLongPress: onNoteLongPressed != null 
                ? () => onNoteLongPressed!(note) 
                : null,
            onTap: onNotePressed != null
                ? () => onNotePressed!(note)
                : null,
          );
        },
      ),
    );
  }
} 