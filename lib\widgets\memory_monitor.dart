import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../services/memory_service.dart';

/// A widget that displays memory usage information for debugging purposes.
/// This should only be used in debug builds.
class MemoryMonitor extends StatefulWidget {
  final Widget child;
  final bool showOverlay;
  
  const MemoryMonitor({
    Key? key,
    required this.child,
    this.showOverlay = kDebugMode, // Only show in debug mode by default
  }) : super(key: key);

  @override
  State<MemoryMonitor> createState() => _MemoryMonitorState();
}

class _MemoryMonitorState extends State<MemoryMonitor> {
  Timer? _timer;
  String _memoryUsage = 'Monitoring...';
  
  @override
  void initState() {
    super.initState();
    if (widget.showOverlay) {
      _startMonitoring();
    }
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  
  void _startMonitoring() {
    // Update memory usage every 2 seconds
    _timer = Timer.periodic(const Duration(seconds: 2), (_) {
      _updateMemoryUsage();
    });
    
    // Initial update
    _updateMemoryUsage();
  }
  
  Future<void> _updateMemoryUsage() async {
    if (!mounted) return;
    
    setState(() {
      _memoryUsage = 'Updating...';
    });
    
    // This is just an approximation as Flutter doesn't provide direct memory usage API
    final info = await _getMemoryInfo();
    
    if (mounted) {
      setState(() {
        _memoryUsage = info;
      });
    }
  }
  
  Future<String> _getMemoryInfo() async {
    // In a real app, you would use platform channels to get actual memory usage
    // For this example, we'll just use a placeholder
    return 'RAM: ~${_getRandomMemoryUsage()} MB';
  }
  
  // Simulated memory usage for demonstration
  String _getRandomMemoryUsage() {
    // In a real app, this would be actual memory usage data
    // For demo purposes, we're using a simulated value
    return (50 + DateTime.now().second % 50).toString();
  }
  
  void _triggerManualGC() {
    final memoryService = Provider.of<MemoryService>(context, listen: false);
    // Clear both caches instead of calling runGarbageCollection
    memoryService.clearCache();
    memoryService.clearImageCache();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Manual memory cleanup triggered'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showOverlay) {
      return widget.child;
    }
    
    return Stack(
      children: [
        widget.child,
        Positioned(
          bottom: 0,
          right: 0,
          child: SafeArea(
            child: Container(
              padding: const EdgeInsets.all(8),
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _memoryUsage,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.cleaning_services, color: Colors.white, size: 16),
                    onPressed: _triggerManualGC,
                    tooltip: 'Trigger memory cleanup',
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
} 