import 'dart:convert';

// Define an enum for reminder repeat types
enum ReminderRepeatType {
  none,
  daily,
  weekly,
  monthly,
  yearly;
  
  @override
  String toString() {
    return name;
  }
  
  static ReminderRepeatType fromString(String? value) {
    if (value == null) return ReminderRepeatType.none;
    return ReminderRepeatType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => ReminderRepeatType.none,
    );
  }
}

class Note {
  final int? id;
  final String title;
  final String content;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? themeColor;
  final bool hasReminder;
  final DateTime? reminderTime;
  final ReminderRepeatType reminderRepeatType;
  final bool isArchived;
  final bool isDeleted;
  final DateTime? deletedAt;
  final bool isPinned;

  Note({
    this.id,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
    this.themeColor,
    this.hasReminder = false,
    this.reminderTime,
    this.reminderRepeatType = ReminderRepeatType.none,
    this.isArchived = false,
    this.isDeleted = false,
    this.deletedAt,
    this.isPinned = false,
  });

  Note copyWith({
    int? id,
    String? title,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? themeColor,
    bool? hasReminder,
    DateTime? reminderTime,
    ReminderRepeatType? reminderRepeatType,
    bool? isArchived,
    bool? isDeleted,
    DateTime? deletedAt,
    bool? isPinned,
  }) {
    return Note(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      themeColor: themeColor ?? this.themeColor,
      hasReminder: hasReminder ?? this.hasReminder,
      reminderTime: reminderTime ?? this.reminderTime,
      reminderRepeatType: reminderRepeatType ?? this.reminderRepeatType,
      isArchived: isArchived ?? this.isArchived,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
      isPinned: isPinned ?? this.isPinned,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'themeColor': themeColor,
      'hasReminder': hasReminder ? 1 : 0,
      'reminderTime': reminderTime?.millisecondsSinceEpoch,
      'reminderRepeatType': reminderRepeatType.name,
      'isArchived': isArchived ? 1 : 0,
      'isDeleted': isDeleted ? 1 : 0,
      'deletedAt': deletedAt?.millisecondsSinceEpoch,
      'isPinned': isPinned ? 1 : 0,
    };
  }

  factory Note.fromMap(Map<String, dynamic> map) {
    return Note(
      id: map['id'],
      title: map['title'] ?? '',
      content: map['content'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      themeColor: map['themeColor'],
      hasReminder: map['hasReminder'] == 1,
      reminderTime: map['reminderTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['reminderTime'])
          : null,
      reminderRepeatType: ReminderRepeatType.fromString(map['reminderRepeatType']),
      isArchived: map['isArchived'] == 1,
      isDeleted: map['isDeleted'] == 1,
      deletedAt: map['deletedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['deletedAt'])
          : null,
      isPinned: map['isPinned'] == 1,
    );
  }

  String toJson() => json.encode(toMap());

  factory Note.fromJson(String source) => Note.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Note(id: $id, title: $title, content: $content, createdAt: $createdAt, updatedAt: $updatedAt, themeColor: $themeColor, hasReminder: $hasReminder, reminderTime: $reminderTime, reminderRepeatType: $reminderRepeatType, isArchived: $isArchived, isDeleted: $isDeleted, deletedAt: $deletedAt, isPinned: $isPinned)';
  }
}
