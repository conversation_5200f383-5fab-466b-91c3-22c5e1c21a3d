import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/foundation.dart';

import '../models/note.dart';
import 'database_service.dart';

class BackupService {
  static final BackupService _instance = BackupService._internal();
  final DatabaseService _databaseService = DatabaseService();

  // Private constructor
  BackupService._internal();

  // Singleton instance
  factory BackupService() => _instance;

  // Current backup version
  static const String _backupVersion = "1.0.0";

  // Create a backup of all notes
  Future<Map<String, dynamic>> _createBackupData() async {
    try {
      // Get all notes
      final List<Note> notes = await _databaseService.getNotes();

      // Create the backup object
      return {
        'version': _backupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'notes': notes.map((note) => note.toMap()).toList(),
      };
    } catch (e) {
      throw Exception('Failed to create backup data: $e');
    }
  }

  // Export backup to a JSON file
  Future<File?> exportBackup() async {
    try {
      final Map<String, dynamic> backupData = await _createBackupData();
      final String backupJson = jsonEncode(backupData);

      // Get the documents directory
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${directory.path}/dark_notes_backup_$timestamp.json';

      // Write the backup to a file
      final File file = File(filePath);
      await file.writeAsString(backupJson);

      return file;
    } catch (e) {
      if (kDebugMode) {
        print('Error exporting backup: $e');
      }
      return null;
    }
  }

  // Share the backup file
  Future<bool> shareBackup() async {
    try {
      final File? backupFile = await exportBackup();
      if (backupFile != null) {
        await Share.shareXFiles(
          [XFile(backupFile.path)],
          subject: 'Dark Notes Backup',
        );
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing backup: $e');
      }
      return false;
    }
  }

  // Import notes from a backup file
  Future<String> importBackup(BuildContext context) async {
    try {
      // Open file picker
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null) {
        return "No file selected";
      }

      final file = File(result.files.single.path!);
      final contents = await file.readAsString();

      // Validate and restore the backup
      final String restoreResult = await _validateAndRestoreBackup(contents);
      return restoreResult;
    } catch (e) {
      return "Error importing backup: $e";
    }
  }

  // Validate the backup file and restore notes
  Future<String> _validateAndRestoreBackup(String backupJson) async {
    try {
      // Parse the backup file
      final Map<String, dynamic> backupData = jsonDecode(backupJson);

      // Validate the backup format
      if (!backupData.containsKey('version') ||
          !backupData.containsKey('timestamp') ||
          !backupData.containsKey('notes')) {
        return "Invalid backup format";
      }

      // Check version compatibility
      final String backupVersion = backupData['version'];
      if (backupVersion != _backupVersion) {
        // For simplicity, just log it, but in a real app you would need version migration logic
        if (kDebugMode) {
          print(
              'Warning: Backup version ($backupVersion) differs from current version ($_backupVersion)');
        }
      }

      // Get the notes from the backup
      final List<dynamic> notesList = backupData['notes'];

      // Clear the database
      await _databaseService.deleteAllNotes();

      // Insert each note from the backup
      for (var noteMap in notesList) {
        final Note note = Note.fromMap(noteMap);
        await _databaseService.insertNote(note);
      }

      return "Backup restored successfully";
    } catch (e) {
      return "Error restoring backup: $e";
    }
  }

  // Create a password-protected backup
  Future<File?> exportEncryptedBackup(String password) async {
    // In a real app, you would encrypt the backup data with the password
    // For this example, we'll just add a password field to the backup data
    try {
      final Map<String, dynamic> backupData = await _createBackupData();
      backupData['isEncrypted'] = true;
      // In a real app, you would encrypt the 'notes' field here

      final String backupJson = jsonEncode(backupData);

      // Get the documents directory
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath =
          '${directory.path}/dark_notes_encrypted_backup_$timestamp.json';

      // Write the backup to a file
      final File file = File(filePath);
      await file.writeAsString(backupJson);

      return file;
    } catch (e) {
      if (kDebugMode) {
        print('Error exporting encrypted backup: $e');
      }
      return null;
    }
  }

  // Import notes from an encrypted backup file
  Future<String> importEncryptedBackup(
      BuildContext context, String password) async {
    // In a real app, you would decrypt the backup data with the password
    try {
      // Open file picker
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null) {
        return "No file selected";
      }

      final file = File(result.files.single.path!);
      final contents = await file.readAsString();

      // Parse the backup file
      final Map<String, dynamic> backupData = jsonDecode(contents);

      // Check if the backup is encrypted
      if (backupData['isEncrypted'] != true) {
        return "This is not an encrypted backup";
      }

      // In a real app, you would decrypt the 'notes' field here with the password

      // Continue with normal restoration
      return await _validateAndRestoreBackup(contents);
    } catch (e) {
      return "Error importing encrypted backup: $e";
    }
  }
}
