import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/note.dart';
import '../models/label.dart';
import '../models/note_label.dart';

class DatabaseService {
  static const String _dbName = 'dark_notes.db';
  static const int _dbVersion = 10;
  static const String tableName = 'notes';
  static const String labelsTableName = 'labels';
  static const String noteLabelsTableName = 'note_labels';
  static final DatabaseService _instance = DatabaseService._internal();

  factory DatabaseService() => _instance;

  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _dbName);
    return await openDatabase(
      path,
      version: _dbVersion, // Updated to version 10 for label system enhancements
      onCreate: _createDb,
      onUpgrade: _upgradeDb,
    );
  }

  Future<void> _createDb(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $tableName(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        content TEXT,
        createdAt INTEGER,
        updatedAt INTEGER,
        themeColor TEXT,
        hasReminder INTEGER,
        reminderTime INTEGER,
        reminderRepeatType TEXT DEFAULT 'none',
        isArchived INTEGER DEFAULT 0,
        isDeleted INTEGER DEFAULT 0,
        deletedAt INTEGER,
        isPinned INTEGER DEFAULT 0
      )
    ''');

    // Create labels table
    await db.execute('''
      CREATE TABLE $labelsTableName(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        color INTEGER NOT NULL,
        isVisible INTEGER DEFAULT 1,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Create note_labels table (many-to-many relationship)
    await db.execute('''
      CREATE TABLE $noteLabelsTableName(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        noteId INTEGER NOT NULL,
        labelId INTEGER NOT NULL,
        FOREIGN KEY (noteId) REFERENCES $tableName(id) ON DELETE CASCADE,
        FOREIGN KEY (labelId) REFERENCES $labelsTableName(id) ON DELETE CASCADE,
        UNIQUE(noteId, labelId)
      )
    ''');
  }

  Future<void> _upgradeDb(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Add isArchived column in version 2
      await db.execute(
          'ALTER TABLE $tableName ADD COLUMN isArchived INTEGER DEFAULT 0');
    }
    
    if (oldVersion < 3) {
      // Add trash-related columns in version 3
      await db.execute(
          'ALTER TABLE $tableName ADD COLUMN isDeleted INTEGER DEFAULT 0');
      await db.execute(
          'ALTER TABLE $tableName ADD COLUMN deletedAt INTEGER');
    }
    
    if (oldVersion < 4) {
      // Add reminderRepeatType column in version 4
      await db.execute(
          'ALTER TABLE $tableName ADD COLUMN reminderRepeatType TEXT DEFAULT \'none\'');
    }

    if (oldVersion < 5) {
      // Create labels table in version 5
      await db.execute('''
        CREATE TABLE $labelsTableName(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          color INTEGER NOT NULL
        )
      ''');

      // Create note_labels table (many-to-many relationship)
      await db.execute('''
        CREATE TABLE $noteLabelsTableName(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          noteId INTEGER NOT NULL,
          labelId INTEGER NOT NULL,
          FOREIGN KEY (noteId) REFERENCES $tableName(id) ON DELETE CASCADE,
          FOREIGN KEY (labelId) REFERENCES $labelsTableName(id) ON DELETE CASCADE,
          UNIQUE(noteId, labelId)
        )
      ''');
    }
    
    if (oldVersion < 10) {
      // Check if the labels table exists and if the isVisible column already exists
      final List<Map<String, dynamic>> tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='$labelsTableName'");
      
      if (tables.isNotEmpty) {
        final List<Map<String, dynamic>> columns = await db.rawQuery("PRAGMA table_info($labelsTableName)");
        final bool hasIsVisible = columns.any((column) => column['name'] == 'isVisible');
        
        if (!hasIsVisible) {
          try {
            // Add new columns for Google Keep style label system
            await db.execute(
              'ALTER TABLE $labelsTableName ADD COLUMN isVisible INTEGER DEFAULT 1',
            );
            await db.execute(
              'ALTER TABLE $labelsTableName ADD COLUMN createdAt INTEGER DEFAULT ${DateTime.now().millisecondsSinceEpoch}',
            );
            await db.execute(
              'ALTER TABLE $labelsTableName ADD COLUMN updatedAt INTEGER DEFAULT ${DateTime.now().millisecondsSinceEpoch}',
            );
            print('Successfully added new columns to labels table');
          } catch (e) {
            print('Error adding columns to labels table: $e');
          }
        }
      }
    }
    
    if (oldVersion < 6) {
      // Add isPinned column in version 6
      await db.execute(
          'ALTER TABLE $tableName ADD COLUMN isPinned INTEGER DEFAULT 0');
    }
  }

  // ===== LABEL OPERATIONS =====

  // Insert a new label
  Future<int> insertLabel(Label label) async {
    final db = await database;
    return await db.insert(
      labelsTableName,
      label.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Get all labels
  Future<List<Label>> getLabels() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      labelsTableName,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Label.fromMap(maps[i]);
    });
  }

  // Get a single label by ID
  Future<Label?> getLabel(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      labelsTableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Label.fromMap(maps.first);
    }
    return null;
  }

  // Update a label
  Future<int> updateLabel(Label label) async {
    final db = await database;
    return await db.update(
      labelsTableName,
      label.toMap(),
      where: 'id = ?',
      whereArgs: [label.id],
    );
  }

  // Delete a label
  Future<int> deleteLabel(int id) async {
    final db = await database;
    
    // First delete all note-label associations for this label
    await db.delete(
      noteLabelsTableName,
      where: 'labelId = ?',
      whereArgs: [id],
    );
    
    // Then delete the label itself
    return await db.delete(
      labelsTableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // ===== NOTE-LABEL RELATIONSHIP OPERATIONS =====

  // Associate a label with a note
  Future<int> addLabelToNote(int noteId, int labelId) async {
    final db = await database;
    final noteLabel = NoteLabel(noteId: noteId, labelId: labelId);
    
    return await db.insert(
      noteLabelsTableName,
      noteLabel.toMap(),
      conflictAlgorithm: ConflictAlgorithm.ignore, // Ignore if the relationship already exists
    );
  }

  // Remove a label from a note
  Future<int> removeLabelFromNote(int noteId, int labelId) async {
    final db = await database;
    return await db.delete(
      noteLabelsTableName,
      where: 'noteId = ? AND labelId = ?',
      whereArgs: [noteId, labelId],
    );
  }

  // Get all labels for a note
  Future<List<Label>> getLabelsForNote(int noteId) async {
    final db = await database;
    
    // Join note_labels with labels to get all labels for a note
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT l.* FROM $labelsTableName l
      INNER JOIN $noteLabelsTableName nl ON l.id = nl.labelId
      WHERE nl.noteId = ?
      ORDER BY l.name ASC
    ''', [noteId]);

    return List.generate(maps.length, (i) {
      return Label.fromMap(maps[i]);
    });
  }

  // Get all notes with a specific label
  Future<List<Note>> getNotesWithLabel(int labelId) async {
    final db = await database;
    
    // Join notes with note_labels to get all notes with a specific label
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT n.* FROM $tableName n
      INNER JOIN $noteLabelsTableName nl ON n.id = nl.noteId
      WHERE nl.labelId = ? AND n.isDeleted = 0
      ORDER BY n.updatedAt DESC
    ''', [labelId]);

    return List.generate(maps.length, (i) {
      return Note.fromMap(maps[i]);
    });
  }
  
  // Get count of notes for each label
  Future<Map<int, int>> getLabelCounts() async {
    final db = await database;
    
    final List<Map<String, dynamic>> results = await db.rawQuery('''
      SELECT nl.labelId, COUNT(DISTINCT nl.noteId) as count
      FROM $noteLabelsTableName nl
      INNER JOIN $tableName n ON nl.noteId = n.id
      WHERE n.isDeleted = 0 AND n.isArchived = 0
      GROUP BY nl.labelId
    ''');
    
    Map<int, int> counts = {};
    for (var row in results) {
      counts[row['labelId']] = row['count'];
    }
    
    return counts;
  }
  
  // Update label visibility
  Future<int> updateLabelVisibility(int labelId, bool isVisible) async {
    final db = await database;
    return await db.update(
      labelsTableName,
      {'isVisible': isVisible ? 1 : 0, 'updatedAt': DateTime.now().millisecondsSinceEpoch},
      where: 'id = ?',
      whereArgs: [labelId],
    );
  }

  // ===== EXISTING NOTES METHODS =====

  Future<int> insertNote(Note note) async {
    final db = await database;
    return await db.insert(
      tableName,
      note.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Note>> getNotes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      orderBy: 'updatedAt DESC',
    );

    return List.generate(maps.length, (i) {
      return Note.fromMap(maps[i]);
    });
  }

  Future<Note?> getNote(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Note.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateNote(Note note) async {
    final db = await database;
    return await db.update(
      tableName,
      note.toMap(),
      where: 'id = ?',
      whereArgs: [note.id],
    );
  }

  // Delete note permanently
  Future<int> deleteNotePermanently(int id) async {
    final db = await database;
    return await db.delete(
      tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }
  
  // Get all notes in trash
  Future<List<Note>> getTrashNotes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      orderBy: 'deletedAt DESC',
      where: 'isDeleted = ?',
      whereArgs: [1],
    );

    return List.generate(maps.length, (i) {
      return Note.fromMap(maps[i]);
    });
  }
  
  // Move note to trash instead of deleting permanently
  Future<int> moveToTrash(int id) async {
    final db = await database;
    return await db.update(
      tableName,
      {
        'isDeleted': 1,
        'deletedAt': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }
  
  // Restore note from trash
  Future<int> restoreFromTrash(int id) async {
    final db = await database;
    return await db.update(
      tableName,
      {
        'isDeleted': 0,
        'deletedAt': null,
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }
  
  // Delete expired notes from trash (older than 7 days)
  Future<int> deleteExpiredNotes() async {
    final db = await database;
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7)).millisecondsSinceEpoch;
    
    return await db.delete(
      tableName,
      where: 'isDeleted = ? AND deletedAt < ?',
      whereArgs: [1, sevenDaysAgo],
    );
  }
  
  // Empty trash (delete all notes in trash)
  Future<int> emptyTrash() async {
    final db = await database;
    return await db.delete(
      tableName,
      where: 'isDeleted = ?',
      whereArgs: [1],
    );
  }
  
  // Delete all notes from database (used by backup service)
  Future<void> deleteAllNotes() async {
    final db = await database;
    await db.delete(tableName);
  }

  // ===== PIN OPERATIONS =====
  
  // Toggle pin status of a note
  Future<int> toggleNotePin(int id, bool isPinned) async {
    final db = await database;
    return await db.update(
      tableName,
      {'isPinned': isPinned ? 1 : 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
