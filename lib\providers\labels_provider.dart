import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/label.dart';
import '../services/database_service.dart';
import '../models/note.dart';

class LabelsProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  List<Label> _labels = [];
  Map<int, int> _labelCounts = {}; // Maps labelId to count of notes with that label
  Set<int> _selectedLabelIds = {}; // Tracks which labels are selected for filtering
  bool _isLoading = false;

  List<Label> get labels => _labels;
  Map<int, int> get labelCounts => _labelCounts;
  Set<int> get selectedLabelIds => _selectedLabelIds;
  bool get isLoading => _isLoading;

  // Get visible labels for drawer
  List<Label> get visibleLabels => _labels.where((label) => label.isVisible).toList();

  LabelsProvider() {
    fetchLabels();
  }

  Future<void> fetchLabels() async {
    _isLoading = true;
    notifyListeners();

    try {
      _labels = await _databaseService.getLabels();
      await fetchLabelCounts();
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching labels: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fetch counts for each label
  Future<void> fetchLabelCounts() async {
    _labelCounts = await _databaseService.getLabelCounts();
    notifyListeners();
  }

  Future<int> addLabel(Label label) async {
    _isLoading = true;
    notifyListeners();

    try {
      final id = await _databaseService.insertLabel(label);
      final newLabel = Label(
        id: id,
        name: label.name,
        color: label.color,
        isVisible: label.isVisible,
        createdAt: label.createdAt,
        updatedAt: label.updatedAt,
      );
      _labels.add(newLabel);
      _sortLabels();
      return id;
    } catch (e) {
      if (kDebugMode) {
        print('Error adding label: $e');
      }
      return -1;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateLabel(Label label) async {
    _isLoading = true;
    notifyListeners();

    try {
      await _databaseService.updateLabel(label);
      final index = _labels.indexWhere((l) => l.id == label.id);
      if (index != -1) {
        _labels[index] = label;
        _sortLabels();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating label: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> deleteLabel(int id) async {
    _isLoading = true;
    notifyListeners();

    try {
      await _databaseService.deleteLabel(id);
      _labels.removeWhere((label) => label.id == id);
      _selectedLabelIds.remove(id);
      await fetchLabelCounts();
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting label: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<List<Label>> getLabelsForNote(int noteId) async {
    try {
      return await _databaseService.getLabelsForNote(noteId);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting labels for note: $e');
      }
      return [];
    }
  }

  Future<void> addLabelToNote(int noteId, int labelId) async {
    try {
      await _databaseService.addLabelToNote(noteId, labelId);
      // Refresh label counts after adding a label to a note
      await fetchLabelCounts();
    } catch (e) {
      if (kDebugMode) {
        print('Error adding label to note: $e');
      }
    }
  }

  Future<void> removeLabelFromNote(int noteId, int labelId) async {
    try {
      await _databaseService.removeLabelFromNote(noteId, labelId);
      // Refresh label counts after removing a label from a note
      await fetchLabelCounts();
    } catch (e) {
      if (kDebugMode) {
        print('Error removing label from note: $e');
      }
    }
  }

  // Add a label to multiple notes at once
  Future<void> addLabelToMultipleNotes(int labelId, Set<int> noteIds) async {
    _isLoading = true;
    notifyListeners();
    
    try {
      for (final noteId in noteIds) {
        await _databaseService.addLabelToNote(noteId, labelId);
      }
      // Refresh label counts after batch operation
      await fetchLabelCounts();
    } catch (e) {
      if (kDebugMode) {
        print('Error adding label to multiple notes: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _sortLabels() {
    _labels.sort((a, b) => a.name.compareTo(b.name));
  }
  
  // Toggle a label's selection state for filtering
  void toggleLabelSelection(int labelId) {
    if (_selectedLabelIds.contains(labelId)) {
      _selectedLabelIds.remove(labelId);
    } else {
      _selectedLabelIds.add(labelId);
    }
    notifyListeners();
  }
  
  // Clear all selected labels
  void clearSelectedLabels() {
    _selectedLabelIds.clear();
    notifyListeners();
  }
  
  // Toggle label visibility in drawer
  Future<void> toggleLabelVisibility(int labelId) async {
    final labelIndex = _labels.indexWhere((label) => label.id == labelId);
    if (labelIndex != -1) {
      final label = _labels[labelIndex];
      final updatedLabel = Label(
        id: label.id,
        name: label.name,
        color: label.color,
        isVisible: !label.isVisible,
        createdAt: label.createdAt,
        updatedAt: DateTime.now(),
      );
      
      await _databaseService.updateLabelVisibility(labelId, !label.isVisible);
      _labels[labelIndex] = updatedLabel;
      notifyListeners();
    }
  }
  
  // Filter notes based on selected labels
  Future<List<Note>> getFilteredNotes() async {
    if (_selectedLabelIds.isEmpty) {
      return [];
    }
    
    Set<Note> filteredNotes = {};
    bool isFirstLabel = true;
    
    for (int labelId in _selectedLabelIds) {
      final notes = await _databaseService.getNotesWithLabel(labelId);
      
      if (isFirstLabel) {
        filteredNotes.addAll(notes);
        isFirstLabel = false;
      } else {
        // For AND filtering (intersection)
        filteredNotes = filteredNotes.intersection(notes.toSet());
      }
      
      if (filteredNotes.isEmpty) {
        break; // No matching notes, exit early
      }
    }
    
    return filteredNotes.toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt)); // Sort by updatedAt desc
  }
  
  // Get all notes with a specific label
  Future<List<Note>> getNotesWithLabel(int labelId) async {
    return await _databaseService.getNotesWithLabel(labelId);
  }
  
  // Get a label by ID
  Label? getLabelById(int labelId) {
    try {
      return _labels.firstWhere((label) => label.id == labelId);
    } catch (e) {
      return null;
    }
  }
  
  // Get count for a specific label
  int getLabelCount(int labelId) {
    return _labelCounts[labelId] ?? 0;
  }
}