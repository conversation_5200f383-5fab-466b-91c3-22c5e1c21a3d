import 'package:flutter/material.dart';

/// Helper utility for responsive layouts
class ResponsiveHelper {
  /// Returns the appropriate grid cross axis count based on screen width
  static int getGridCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < 600) {
      return 2; // Phone
    } else if (width < 900) {
      return 3; // Small tablet
    } else if (width < 1200) {
      return 4; // Large tablet
    } else {
      return 5; // Desktop
    }
  }
  
  /// Returns appropriate spacing based on screen size
  static double getSpacing(BuildContext context, {bool isHorizontal = true}) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < 600) {
      return isHorizontal ? 8.0 : 8.0; // Phone
    } else if (width < 900) {
      return isHorizontal ? 12.0 : 10.0; // Small tablet
    } else {
      return isHorizontal ? 16.0 : 12.0; // Large devices
    }
  }
  
  /// Returns appropriate font size based on design size and screen width
  static double getAdaptiveFontSize(BuildContext context, double fontSize) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < 360) {
      return fontSize * 0.8; // Very small phone
    } else if (width < 600) {
      return fontSize * 1.0; // Normal phone
    } else if (width < 900) {
      return fontSize * 1.1; // Tablet
    } else {
      return fontSize * 1.2; // Large screen
    }
  }
  
  /// Returns the appropriate content padding based on screen size
  static EdgeInsets getContentPadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < 600) {
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0);
    } else if (width < 900) {
      return const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0);
    } else {
      return const EdgeInsets.symmetric(horizontal: 32.0, vertical: 20.0);
    }
  }
  
  /// Returns whether the current device is a mobile phone
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < 600;
  }
  
  /// Returns whether the current device is a tablet
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 600 && width < 1200;
  }
  
  /// Returns whether the current device is a desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1200;
  }
  
  /// Returns appropriate app bar height based on device
  static double getAppBarHeight(BuildContext context) {
    if (isDesktop(context)) {
      return 64.0;
    } else if (isTablet(context)) {
      return 56.0;
    } else {
      return 48.0;
    }
  }
} 