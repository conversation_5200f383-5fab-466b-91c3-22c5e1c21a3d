import 'package:flutter/material.dart';

class Label {
  final int? id;
  final String name;
  final Color color;
  final bool isVisible;
  final DateTime createdAt;
  final DateTime updatedAt;

  Label({
    this.id,
    required this.name,
    required this.color,
    this.isVisible = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : 
    this.createdAt = createdAt ?? DateTime.now(),
    this.updatedAt = updatedAt ?? DateTime.now();

  Label copyWith({
    int? id,
    String? name,
    Color? color,
    bool? isVisible,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Label(
      id: id ?? this.id,
      name: name ?? this.name,
      color: color ?? this.color,
      isVisible: isVisible ?? this.isVisible,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'color': color.value,
      'isVisible': isVisible ? 1 : 0,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory Label.fromMap(Map<String, dynamic> map) {
    return Label(
      id: map['id'],
      name: map['name'] ?? '',
      color: Color(map['color']),
      isVisible: map['isVisible'] == 1,
      createdAt: map['createdAt'] != null ? DateTime.fromMillisecondsSinceEpoch(map['createdAt']) : null,
      updatedAt: map['updatedAt'] != null ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt']) : null,
    );
  }

  @override
  String toString() {
    return 'Label(id: $id, name: $name, color: $color)';
  }
} 