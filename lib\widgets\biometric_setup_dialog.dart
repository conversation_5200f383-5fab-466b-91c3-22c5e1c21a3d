import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

/// A dialog for verifying PIN before enabling biometric authentication
class BiometricSetupDialog extends StatefulWidget {
  final Function(String) onPinSubmitted;
  final Function() onCancel;

  const BiometricSetupDialog({
    Key? key,
    required this.onPinSubmitted,
    required this.onCancel,
  }) : super(key: key);

  @override
  State<BiometricSetupDialog> createState() => _BiometricSetupDialogState();
}

class _BiometricSetupDialogState extends State<BiometricSetupDialog> {
  final TextEditingController _pinController = TextEditingController();
  final FocusNode _pinFocusNode = FocusNode();
  bool _isPinError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Request focus on PIN field when dialog opens
    Future.microtask(() => _pinFocusNode.requestFocus());
  }

  @override
  void dispose() {
    _pinController.dispose();
    _pinFocusNode.dispose();
    super.dispose();
  }

  void _onPinSubmitted(String pin) {
    if (pin.length == 4) {
      Navigator.of(context).pop();
      widget.onPinSubmitted(pin);
    } else {
      setState(() {
        _isPinError = true;
        _errorMessage = 'Please enter your 4-digit PIN';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Get screen size to make responsive adjustments
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.height < 600;
    
    // Calculate responsive values
    final iconSize = isSmallScreen ? 36.0 : 48.0;
    final titleFontSize = isSmallScreen ? 16.0 : 18.0;
    final contentFontSize = isSmallScreen ? 12.0 : 14.0;
    final verticalSpacing = isSmallScreen ? 12.0 : 24.0;
    
    // Calculate PIN field dimensions based on available width
    final dialogWidth = screenSize.width * 0.8; // Dialog typically takes ~80% of screen width
    final availableWidth = dialogWidth - 48.0; // Subtract padding
    final fieldWidth = (availableWidth / 5.5); // Reduce field width to create more spacing
    final fieldHeight = isSmallScreen ? 40.0 : 50.0;
    
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      contentPadding: EdgeInsets.symmetric(
        horizontal: 20.0,
        vertical: isSmallScreen ? 12.0 : 20.0,
      ),
      // Use SingleChildScrollView to handle potential overflow
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.security,
              size: iconSize,
              color: Theme.of(context).primaryColor,
            ),
            SizedBox(height: isSmallScreen ? 8.0 : 12.0),
            Text(
              'Verify Your PIN',
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isSmallScreen ? 8.0 : 12.0),
            Text(
              'For your security, please enter your PIN to enable biometric authentication.',
              style: TextStyle(
                fontSize: contentFontSize,
                color: isDarkMode ? Colors.white70 : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: verticalSpacing),
            
            // Wrap the PIN input in a Container to handle styling
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 4.0),
              child: PinCodeTextField(
                appContext: context,
                length: 4,
                controller: _pinController,
                focusNode: _pinFocusNode,
                obscureText: true,
                autoFocus: true,
                animationType: AnimationType.fade,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                backgroundColor: Colors.transparent, // Remove background
                keyboardType: TextInputType.number,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  borderRadius: BorderRadius.circular(8),
                  fieldHeight: fieldHeight,
                  fieldWidth: fieldWidth,
                  activeFillColor: isDarkMode ? Colors.black12 : Colors.white,
                  activeColor: Theme.of(context).primaryColor,
                  inactiveColor: isDarkMode ? Colors.grey[700] : Colors.grey[300],
                  selectedColor: Theme.of(context).primaryColor,
                  selectedFillColor: isDarkMode ? Colors.black12 : Colors.grey[100],
                  inactiveFillColor: isDarkMode ? Colors.black12 : Colors.grey[100],
                  borderWidth: 1.5,
                ),
                cursorColor: Theme.of(context).primaryColor,
                animationDuration: const Duration(milliseconds: 300),
                enableActiveFill: true,
                onCompleted: _onPinSubmitted,
                onChanged: (value) {
                  if (_isPinError) {
                    setState(() {
                      _isPinError = false;
                      _errorMessage = '';
                    });
                  }
                },
              ),
            ),
            
            if (_isPinError)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  _errorMessage,
                  style: TextStyle(
                    color: Colors.red[400],
                    fontSize: 12,
                  ),
                ),
              ),
            // Row of buttons instead of using the actions property
            Padding(
              padding: EdgeInsets.only(top: isSmallScreen ? 8.0 : 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      widget.onCancel();
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey[600],
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 8.0 : 16.0,
                        vertical: isSmallScreen ? 4.0 : 8.0,
                      ),
                    ),
                    child: const Text('CANCEL'),
                  ),
                  TextButton(
                    onPressed: () => _onPinSubmitted(_pinController.text),
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).primaryColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 8.0 : 16.0,
                        vertical: isSmallScreen ? 4.0 : 8.0,
                      ),
                    ),
                    child: const Text('CONFIRM'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      // Remove actions since we've added them to the content
      actions: [],
    );
  }
} 