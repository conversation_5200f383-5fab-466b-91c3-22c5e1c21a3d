import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'dart:io';
import 'providers/notes_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/labels_provider.dart';
import 'providers/app_lock_provider.dart';
import 'models/label.dart';
import 'screens/home_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/archive_screen.dart';
import 'screens/trash_screen.dart';
import 'screens/labels_screen.dart';
import 'screens/label_notes_screen.dart';
import 'screens/app_lock_screen.dart';
import 'utils/theme_config.dart';
import 'utils/navigation_utils.dart';
import 'widgets/app_lifecycle_manager.dart';
import 'widgets/memory_optimizer.dart';
import 'services/secure_storage_service.dart';
import 'services/auth_service.dart';
import 'services/unified_backup_service.dart';
import 'services/memory_service.dart';
import 'services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize sqflite_ffi for desktop platforms
  if (Platform.isWindows || Platform.isLinux) {
    // Initialize FFI
    sqfliteFfiInit();
    // Change the default factory
    databaseFactory = databaseFactoryFfi;
  }

  // Initialize services
  final secureStorage = SecureStorageService();
  final authService = AuthService();
  final backupService = UnifiedBackupService();
  final memoryService = MemoryService();

  // Initialize notification service
  await NotificationService().initialize();

  // Initialize memory service
  memoryService.initialize();

  runApp(MyApp(
    secureStorage: secureStorage,
    authService: authService,
    backupService: backupService,
    memoryService: memoryService,
  ));
}

class MyApp extends StatelessWidget {
  final SecureStorageService secureStorage;
  final AuthService authService;
  final UnifiedBackupService backupService;
  final MemoryService memoryService;

  const MyApp({
    super.key,
    required this.secureStorage,
    required this.authService,
    required this.backupService,
    required this.memoryService,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<SecureStorageService>.value(value: secureStorage),
        Provider<AuthService>.value(value: authService),
        Provider<UnifiedBackupService>.value(value: backupService),
        Provider<MemoryService>.value(value: memoryService),
        ChangeNotifierProvider(create: (_) => NotesProvider(memoryService: memoryService)),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => LabelsProvider()),
        ChangeNotifierProvider(create: (context) => AppLockProvider(
          secureStorage: secureStorage,
          authService: authService,
        )),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) {
          return AnimatedTheme(
            duration: const Duration(milliseconds: 200),
            data: themeProvider.themeMode == ThemeMode.dark
                ? AppThemes.darkTheme
                : AppThemes.lightTheme,
            child: MaterialApp(
              title: 'Dark Notes',
              debugShowCheckedModeBanner: false,
              theme: AppThemes.lightTheme,
              darkTheme: AppThemes.darkTheme,
              themeMode: themeProvider.themeMode,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              FlutterQuillLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', 'US'),
            ],
            home: MemoryOptimizer(
              memoryService: memoryService,
              child: AppLifecycleManager(
                child: AppLockScreen(
                  child: HomeScreen(),
                ),
              ),
            ),
            // Use onGenerateRoute for all routes to wrap them with AppLockScreen
            onGenerateRoute: (settings) {
              // Get the route based on the name
              Widget? pageWidget;

              // Handle named routes
              if (settings.name == LabelsScreen.routeName) {
                pageWidget = const LabelsScreen();
              } else if (settings.name == LabelNotesScreen.routeName) {
                final label = settings.arguments as Label;
                pageWidget = LabelNotesScreen(label: label);
              }

              // If we have a valid page widget, wrap it with the necessary providers
              if (pageWidget != null) {
                // Create a secure route manually since we can't use NavigationUtils here
                // (context is not available at this level)
                return MaterialPageRoute(
                  settings: settings,
                  builder: (context) => MemoryOptimizer(
                    memoryService: memoryService,
                    child: AppLifecycleManager(
                      child: AppLockScreen(
                        child: pageWidget!,
                      ),
                    ),
                  ),
                );
              }

              // Return null to let Flutter use the default route handling
              return null;
            },
            ),
          );
        },
      ),
    );
  }
}
