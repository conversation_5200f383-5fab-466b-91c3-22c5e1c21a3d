import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';

/// A fullscreen image viewer with delete functionality
class FullscreenImageViewer extends StatefulWidget {
  final String imageUrl;
  final QuillController? controller;
  final VoidCallback? onDeleteImage;

  const FullscreenImageViewer({
    Key? key,
    required this.imageUrl,
    this.controller,
    this.onDeleteImage,
  }) : super(key: key);
  
  @override
  State<FullscreenImageViewer> createState() => _FullscreenImageViewerState();
}

class _FullscreenImageViewerState extends State<FullscreenImageViewer> {
  // Track if we're currently deleting to avoid double-deletion
  bool _isDeleting = false;

  // Helper method to delete the image from a quill document
  void _deleteImageFromDocument() {
    if (_isDeleting || widget.controller == null) return;
    _isDeleting = true;
    
    try {
      final controller = widget.controller!;
      final imageUrl = widget.imageUrl;
      
      // We need to find the image in the editor document
      final Delta delta = controller.document.toDelta();
      final json = delta.toJson();
      
      int? foundIndex;
      int offset = 0;
      
      // Loop through operations to find the image
      for (int i = 0; i < json.length; i++) {
        final op = json[i];
        // Check if this operation contains our image
        if (op.containsKey('insert') && 
            op['insert'] is Map && 
            op['insert'].containsKey('image') && 
            op['insert']['image'] == imageUrl) {
          foundIndex = i;
          break;
        }
        
        // Calculate offset
        if (op.containsKey('insert')) {
          if (op['insert'] is String) {
            offset += op['insert'].length as int;
          } else {
            offset += 1; // Embedded objects count as 1
          }
        }
      }
      
      // If image found, delete it
      if (foundIndex != null) {
        controller.replaceText(offset, 1, '', null);
      }
      
      Navigator.of(context).pop();
    } catch (e) {
      // Show error if something went wrong
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete image: $e'),
          duration: const Duration(seconds: 3),
        ),
      );
    } finally {
      _isDeleting = false;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: Container(
        color: Colors.black,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Image
            Center(
              child: Hero(
                tag: widget.imageUrl,
                child: InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: widget.imageUrl.startsWith('http') 
                      ? Image.network(widget.imageUrl, fit: BoxFit.contain)
                      : Image.file(File(widget.imageUrl), fit: BoxFit.contain),
                ),
              ),
            ),
            // Close button at top
            Positioned(
              top: 16,
              right: 16,
              child: IconButton(
                icon: const Icon(Icons.close, color: Colors.white, size: 28),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
            // Modern delete button
            Positioned(
              bottom: 32,
              right: 0,
              left: 0,
              child: Center(
                child: Container(
                  height: 56,
                  width: 56,
                  decoration: BoxDecoration(
                    color: Colors.red.shade600,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      customBorder: const CircleBorder(),
                      onTap: () {
                        // Use custom delete method if provided, otherwise try built-in method
                        if (widget.onDeleteImage != null) {
                          widget.onDeleteImage!();
                          Navigator.of(context).pop();
                        } else {
                          _deleteImageFromDocument();
                        }
                      },
                      child: const Center(
                        child: Icon(
                          Icons.delete_outline_rounded,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
