import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_lifecycle_manager.dart';
import '../widgets/memory_optimizer.dart';
import '../screens/app_lock_screen.dart';
import '../services/memory_service.dart';

/// A utility class for navigation functions
class NavigationUtils {
  /// Creates a secure route that wraps the destination screen with AppLockScreen
  /// and other necessary providers to ensure app lock is enforced consistently.
  static Route<T> createSecureRoute<T>({
    required BuildContext context,
    required Widget screen,
    RouteSettings? settings,
    bool fullscreenDialog = false,
  }) {
    // Get the memory service from the provider
    final memoryService = Provider.of<MemoryService>(context, listen: false);
    
    return MaterialPageRoute<T>(
      settings: settings,
      fullscreenDialog: fullscreenDialog,
      builder: (context) => MemoryOptimizer(
        memoryService: memoryService,
        child: AppLifecycleManager(
          child: AppLockScreen(
            child: screen,
          ),
        ),
      ),
    );
  }
} 