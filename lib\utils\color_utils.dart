import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Utility class for color-related functionality
class ColorUtils {
  /// Determines if a color is dark or light
  static bool isDarkColor(Color color) {
    return ThemeData.estimateBrightnessForColor(color) == Brightness.dark;
  }
  
  /// Creates a darker variant of a color
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    
    return hslDark.toColor();
  }
  
  /// Creates a lighter variant of a color
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    
    return hslLight.toColor();
  }
  
  /// Adjusts saturation of a color
  static Color adjustSaturation(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final adjustedHsl = hsl.withSaturation((hsl.saturation * amount).clamp(0.0, 1.0));
    
    return adjustedHsl.toColor();
  }
  
  /// Creates a harmonized color palette from a primary color
  static MaterialColorPalette createMaterialPalette(Color primaryColor) {
    return MaterialColorPalette(primaryColor);
  }
  
  /// Creates a gradient based on a color
  static LinearGradient createGradient(Color color, {bool isDark = false}) {
    final Color startColor = lighten(color, 0.1);
    final Color endColor = darken(color, 0.05);
    
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        startColor.withOpacity(isDark ? 0.2 : 0.3),
        endColor.withOpacity(isDark ? 0.1 : 0.2),
      ],
    );
  }
  
  /// Creates a subtle texture overlay for cards
  static Shader createNoiseTexture(Rect bounds) {
    const double patternSize = 100;
    final math.Random random = math.Random(42); // Fixed seed for consistent pattern
    
    final List<Color> colors = List.generate(
      20,
      (i) => Colors.white.withOpacity(0.01 + random.nextDouble() * 0.03),
    );
    
    final List<double> stops = List.generate(
      colors.length,
      (i) => i / colors.length,
    );
    
    return LinearGradient(
      begin: const Alignment(-1.0, -1.0),
      end: const Alignment(1.0, 1.0),
      colors: colors,
      stops: stops,
      tileMode: TileMode.mirror,
    ).createShader(bounds);
  }
  
  /// Creates a shadow for cards
  static List<BoxShadow> createCardShadow(Color color, {double elevation = 2.0}) {
    return [
      BoxShadow(
        color: color.withOpacity(0.2),
        blurRadius: elevation * 2,
        spreadRadius: elevation * 0.3,
        offset: Offset(0, elevation * 0.5),
      ),
      BoxShadow(
        color: color.withOpacity(0.1),
        blurRadius: elevation * 4,
        spreadRadius: elevation * 0.2,
        offset: Offset(0, elevation * 0.3),
      ),
    ];
  }
}

/// Material You inspired color palette
class MaterialColorPalette {
  final Color primary;
  late Color secondary;
  late Color tertiary;
  late Color error;
  late Color surface;
  late Color background;
  
  MaterialColorPalette(this.primary) {
    // Create a harmonic palette based on the primary color
    final hslPrimary = HSLColor.fromColor(primary);
    
    // Secondary color (complementary hue)
    secondary = HSLColor.fromAHSL(
      1.0,
      (hslPrimary.hue + 180) % 360, // Opposite hue
      hslPrimary.saturation * 0.9,   // Slightly less saturated
      hslPrimary.lightness,
    ).toColor();
    
    // Tertiary color (triadic harmony)
    tertiary = HSLColor.fromAHSL(
      1.0,
      (hslPrimary.hue + 120) % 360, // 120 degrees offset
      hslPrimary.saturation * 0.8,
      hslPrimary.lightness,
    ).toColor();
    
    // Error color (warm red tone that complements the palette)
    error = Colors.red.shade700;
    
    // Surface and background colors (based on primary but very subtle)
    final bool isDark = ColorUtils.isDarkColor(primary);
    surface = isDark ? Colors.grey.shade900 : Colors.grey.shade50;
    background = isDark ? Colors.black : Colors.white;
  }
} 