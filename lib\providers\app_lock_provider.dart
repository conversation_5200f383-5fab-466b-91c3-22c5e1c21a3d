import 'dart:async';
import 'package:flutter/material.dart';
import '../services/secure_storage_service.dart';
import '../services/auth_service.dart';

/// A provider class that manages the app lock state and settings
class AppLockProvider extends ChangeNotifier {
  final SecureStorageService _secureStorage;
  final AuthService authService; // Made public for access from settings

  bool _isInitialized = false;
  bool _isLockEnabled = false;
  bool _isBiometricEnabled = false;
  String _lockTimeout = 'immediately';
  DateTime? _lastActiveTimestamp;
  bool _isAuthenticated = false;

  // Track if we are currently checking authentication state
  bool _isCheckingAuthState = false;

  // Track if the app was truly backgrounded
  bool _wasBackgrounded = false;
  
  // Archive lock properties
  bool _isArchiveLocked = false;
  bool _useAppPinForArchive = true;
  bool _isArchiveAuthenticated = false;

  AppLockProvider({
    required SecureStorageService secureStorage,
    required AuthService authService,
  })  : _secureStorage = secureStorage,
        authService = authService;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isLockEnabled => _isLockEnabled;
  bool get isBiometricEnabled => _isBiometricEnabled;
  String get lockTimeout => _lockTimeout;
  bool get isAuthenticated => _isAuthenticated;
  bool get isCheckingAuthState => _isCheckingAuthState;
  
  // Archive lock getters
  bool get isArchiveLocked => _isArchiveLocked;
  bool get useAppPinForArchive => _useAppPinForArchive;
  bool get isArchiveAuthenticated => _isArchiveAuthenticated || _isAuthenticated;

  /// Initialize the provider by loading saved preferences
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Load app lock status
    _isLockEnabled = await _secureStorage.isLockEnabled();
    
    // Only load other app lock settings if lock is enabled
    if (_isLockEnabled) {
      _isBiometricEnabled = await _secureStorage.isBiometricEnabled();
      _lockTimeout = await _secureStorage.getLockTimeout();
      _lastActiveTimestamp = await _secureStorage.getLastActiveTimestamp();
    }
    
    // Load archive lock settings
    _isArchiveLocked = await _secureStorage.isArchiveLocked();
    if (_isArchiveLocked) {
      _useAppPinForArchive = await _secureStorage.useAppPinForArchive();
      // No need to load timestamp since we always authenticate
    }
    
    _isInitialized = true;
    notifyListeners();
  }

  /// Check if authentication is required based on timeout settings
  Future<bool> isAuthenticationRequired() async {
    if (!_isInitialized) await initialize();
    
    // If lock is not enabled, no need to authenticate
    if (!_isLockEnabled) return false;
    
    // If already authenticated and 'immediately' is not set, no need to authenticate
    if (_isAuthenticated && _lockTimeout != 'immediately') return false;

    // 'immediately' always requires authentication when switching apps
    if (_lockTimeout == 'immediately') return true;

    // Check if the timeout period has passed since last active timestamp
    if (_lastActiveTimestamp != null) {
      final now = DateTime.now();
      final difference = now.difference(_lastActiveTimestamp!);
      
      // Convert timeout string to duration
      final timeoutDuration = _parseTimeoutString(_lockTimeout);
      
      // If the difference is greater than timeout duration, auth is required
      final requiresAuth = difference > timeoutDuration;
      
      return requiresAuth;
    }
    
    // If no timestamp is available, require authentication as a fallback
    return true;
  }

  /// Parse the timeout string to a Duration object
  Duration _parseTimeoutString(String timeout) {
    switch (timeout) {
      case 'immediately':
        return Duration.zero;
      case '1 minute':
        return const Duration(minutes: 1);
      case '5 minutes':
        return const Duration(minutes: 5);
      case '10 minutes':
        return const Duration(minutes: 10);
      case '30 minutes':
        return const Duration(minutes: 30);
      case '1 hour':
        return const Duration(hours: 1);
      default:
        return Duration.zero; // Default to immediate lock if invalid timeout
    }
  }

  /// Authenticate using PIN
  Future<bool> authenticateWithPin(String pin) async {
    final storedPin = await _secureStorage.getPin();
    if (storedPin == pin) {
      _isAuthenticated = true;
      await _updateLastActiveTimestamp();
      notifyListeners();
      return true;
    }
    return false;
  }

  /// Authenticate using biometrics
  Future<bool> authenticateWithBiometrics() async {
    print("DEBUG: AppLockProvider: Starting biometric authentication");
    if (!_isBiometricEnabled) {
      print("DEBUG: AppLockProvider: Biometrics not enabled in app settings");
      return false;
    }
    
    try {
      print("DEBUG: AppLockProvider: Requesting biometric authentication");
      final result = await authService.authenticateWithBiometrics(
        localizedReason: 'Unlock Dark Notes',
      );
      
      print("DEBUG: AppLockProvider: Authentication result: $result");
      
      if (result == AuthResult.success) {
        // CRITICAL FIX: More direct authentication state management
        print("DEBUG: AppLockProvider: SUCCESSFUL AUTHENTICATION DETECTED");
        
        // Set authentication state directly
        _isAuthenticated = true;
        print("DEBUG: AppLockProvider: Set _isAuthenticated = $_isAuthenticated");
        
        // Update timestamp
        await _updateLastActiveTimestamp();
        
        // Force immediate UI update
        print("DEBUG: AppLockProvider: Triggering UI update");
        notifyListeners();
        
        // Make sure change is detected
        await Future.delayed(const Duration(milliseconds: 50));
        print("DEBUG: AppLockProvider: Authentication state after delay: $_isAuthenticated");
        
        // Additional notification to ensure UI update
        notifyListeners();
        
        return true;
      } else if (result == AuthResult.canceled) {
        // User explicitly canceled - allow falling back to PIN
        print("DEBUG: AppLockProvider: Authentication was canceled by user");
        return false;
      } else {
        // Other failures
        print("DEBUG: AppLockProvider: Authentication failed: $result");
        return false;
      }
    } catch (e) {
      print('DEBUG: AppLockProvider: Authentication error: $e');
      return false;
    }
  }

  /// Force authentication (for testing or direct authentication)
  void forceAuthentication() {
    print("DEBUG: AppLockProvider: FORCE AUTHENTICATION called");
    _isAuthenticated = true;
    _updateLastActiveTimestamp();
    notifyListeners();
    print("DEBUG: AppLockProvider: Force set _isAuthenticated = $_isAuthenticated");
  }

  /// Set up a PIN for app lock
  Future<bool> setupPin(String pin) async {
    try {
      await _secureStorage.setPin(pin);
      await _secureStorage.setLockEnabled(true);
      _isLockEnabled = true;
      _isAuthenticated = true;
      await _updateLastActiveTimestamp();
      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Change the existing PIN
  Future<bool> changePin({required String currentPin, required String newPin}) async {
    // Verify current PIN first
    final isValid = await authenticateWithPin(currentPin);
    if (!isValid) return false;
    
    // Update PIN
    await _secureStorage.setPin(newPin);
    return true;
  }

  /// Remove PIN and disable app lock
  Future<bool> removePin(String pin) async {
    // Verify PIN first
    final isValid = await authenticateWithPin(pin);
    if (!isValid) return false;
    
    // Remove PIN and disable lock
    await _secureStorage.deletePin();
    await _secureStorage.setLockEnabled(false);
    await _secureStorage.setBiometricEnabled(false);
    
    _isLockEnabled = false;
    _isBiometricEnabled = false;
    notifyListeners();
    
    return true;
  }

  /// Toggle biometric authentication
  Future<bool> toggleBiometrics(bool enabled) async {
    // Check if biometrics are available on the device
    if (enabled) {
      final biometricStatus = await authService.getBiometricStatus();
      
      if (biometricStatus == BiometricStatus.unsupported) {
        return false;
      }
      
      // If biometrics are not enrolled, we will allow enabling the setting
      // but the UI should guide users to enroll biometrics at the OS level
      if (biometricStatus == BiometricStatus.notEnrolled) {
        // Return a special code to indicate the need for enrollment guidance
        // The setting will be enabled, but the UI should show guidance
        await _secureStorage.setBiometricEnabled(enabled);
        await _secureStorage.setBiometricEnrollmentNeeded(true);
        _isBiometricEnabled = enabled;
        notifyListeners();
        return true;
      }
      
      // Biometrics are properly set up, we can enable them
      await _secureStorage.setBiometricEnabled(enabled);
      await _secureStorage.setBiometricEnrollmentNeeded(false);
      _isBiometricEnabled = enabled;
      notifyListeners();
    } else {
      // Disabling biometrics doesn't need special handling
      await _secureStorage.setBiometricEnabled(enabled);
      _isBiometricEnabled = enabled;
      notifyListeners();
    }
    
    return true;
  }
  
  /// Toggle biometric authentication with PIN verification first
  Future<Map<String, dynamic>> toggleBiometricsWithVerification(bool enabled, String pin) async {
    // First verify the PIN
    final isValidPin = await authenticateWithPin(pin);
    if (!isValidPin) {
      return {
        'success': false,
        'reason': 'invalid_pin',
        'message': 'The PIN you entered is incorrect.'
      };
    }
    
    // Check biometric status
    if (enabled) {
      final biometricStatus = await authService.getBiometricStatus();
      
      if (biometricStatus == BiometricStatus.unsupported) {
        return {
          'success': false, 
          'reason': 'unsupported',
          'message': 'Your device doesn\'t support biometric authentication.'
        };
      }
      
      // Enable biometrics in all cases - even if enrollment is needed
      await _secureStorage.setBiometricEnabled(enabled);
      _isBiometricEnabled = enabled;
      
      if (biometricStatus == BiometricStatus.notEnrolled) {
        // Mark that enrollment is needed
        await _secureStorage.setBiometricEnrollmentNeeded(true);
        notifyListeners();
        
        // Return needs_enrollment to trigger the guidance dialog
        return {
          'success': true,
          'reason': 'needs_enrollment',
          'message': 'Please set up biometrics in your device settings first.'
        };
      }
      
      // Biometrics are enrolled and ready
      await _secureStorage.setBiometricEnrollmentNeeded(false);
      notifyListeners();
      
      return {'success': true, 'reason': 'enabled'};
    } else {
      // Disabling biometrics
      await _secureStorage.setBiometricEnabled(enabled);
      _isBiometricEnabled = enabled;
      notifyListeners();
      
      return {'success': true, 'reason': 'disabled'};
    }
  }
  
  /// Check if biometric enrollment is needed
  Future<bool> isBiometricEnrollmentNeeded() async {
    return await _secureStorage.isBiometricEnrollmentNeeded();
  }
  
  /// Silently update biometric enrollment status
  /// Used when biometrics are enrolled after showing guidance
  Future<void> silentlyUpdateBiometricEnrollment() async {
    await _secureStorage.setBiometricEnrollmentNeeded(false);
  }

  /// Set the lock timeout preference
  Future<void> setLockTimeout(String timeout) async {
    await _secureStorage.setLockTimeout(timeout);
    _lockTimeout = timeout;
    notifyListeners();
  }

  /// Update the last active timestamp
  Future<void> _updateLastActiveTimestamp() async {
    final now = DateTime.now();
    _lastActiveTimestamp = now;
    await _secureStorage.updateLastActiveTimestamp();
  }

  /// Called when app goes to background
  Future<void> appToBackground() async {
    if (_isLockEnabled) {
      // Record the exact time when app goes to background
      final now = DateTime.now();
      _lastActiveTimestamp = now;
      await _secureStorage.updateLastActiveTimestamp();
      // Mark that we've been backgrounded
      _wasBackgrounded = true;
      
      // Log for debugging
      print("DEBUG: AppLockProvider: App went to background at $now");
    }
  }

  //========== Archive Lock Methods ==========//

  /// Enable or disable archive lock
  Future<void> setArchiveLocked(bool enabled) async {
    await _secureStorage.setArchiveLocked(enabled);
    _isArchiveLocked = enabled;
    
    // If disabling, clear archive authentication state
    if (!enabled) {
      _isArchiveAuthenticated = false;
    }
    
    notifyListeners();
  }

  /// Set whether to use app PIN for archive
  Future<void> setUseAppPinForArchive(bool value) async {
    await _secureStorage.setUseAppPinForArchive(value);
    _useAppPinForArchive = value;
    notifyListeners();
  }

  /// Setup custom PIN for archive access
  Future<bool> setupArchivePin(String pin) async {
    try {
      await _secureStorage.setArchivePin(pin);
      await _secureStorage.setUseAppPinForArchive(false);
      _useAppPinForArchive = false;
      // Do NOT mark as authenticated when setting up a new PIN
      // This ensures users need to authenticate when accessing archive
      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if authentication is required for archive access
  Future<bool> isArchiveAuthenticationRequired() async {
    if (!_isInitialized) await initialize();
    
    // If archive lock is not enabled, no need to authenticate
    if (!_isArchiveLocked) return false;
    
    // If the user is already authenticated specifically for the archive, no need to re-authenticate
    if (_isArchiveAuthenticated) return false;
    
    // If using app PIN for archive and the user is already authenticated for the main app,
    // allow access without requiring re-authentication
    if (_isAuthenticated && _useAppPinForArchive) return false;

    // In all other cases, require authentication for archive access
    return true;
  }

  /// Authenticate archive access using PIN
  Future<bool> authenticateArchiveWithPin(String pin) async {
    // If using app PIN for archive, check against app PIN
    if (_useAppPinForArchive) {
      final storedPin = await _secureStorage.getPin();
      if (storedPin == pin) {
        _isArchiveAuthenticated = true;
        notifyListeners();
        return true;
      }
    } else {
      // Check against custom archive PIN
      final storedPin = await _secureStorage.getArchivePin();
      if (storedPin == pin) {
        _isArchiveAuthenticated = true;
        notifyListeners();
        return true;
      }
    }
    return false;
  }

  /// Authenticate archive using biometrics
  Future<bool> authenticateArchiveWithBiometrics() async {
    if (!_isBiometricEnabled) return false;
    
    final isAvailable = await authService.isBiometricAvailable();
    if (!isAvailable) return false;

    final result = await authService.authenticateWithBiometrics(
      localizedReason: 'Authenticate to access your archived notes',
    );
    
    final success = result == AuthResult.success;
    if (success) {
      _isArchiveAuthenticated = true;
      notifyListeners();
    }
    
    return success;
  }

  // We no longer use archive timestamps since we require authentication on every archive access

  /// Called when app comes to foreground
  Future<void> appToForeground() async {
    // Log for debugging
    print("DEBUG: AppLockProvider: App came to foreground, wasBackgrounded=$_wasBackgrounded");
    
    if (_isLockEnabled) {
      // Set state to indicate we are checking authentication
      _isCheckingAuthState = true;
      notifyListeners();
      
      // Force reevaluation of authentication requirement
      // Important: Use a local variable here, don't update class property yet
      final needsAuth = await isAuthenticationRequired();
      print("DEBUG: AppLockProvider: Authentication required: $needsAuth");
      
      // If authentication is required, reset the authenticated state
      if (needsAuth) {
        print("DEBUG: AppLockProvider: Resetting authentication state");
        _isAuthenticated = false;
      }
      
      // Done checking auth state
      _isCheckingAuthState = false;
      
      // Always notify listeners to ensure UI updates
      // This is crucial - we must notify even if auth state didn't change
      notifyListeners();
      
      // Check if biometric enrollment status has changed
      if (_isBiometricEnabled) {
        final enrollmentNeeded = await _secureStorage.isBiometricEnrollmentNeeded();
        if (enrollmentNeeded) {
          // Check if biometrics are now enrolled
          final biometricStatus = await authService.getBiometricStatus();
          if (biometricStatus == BiometricStatus.enrolled) {
            await _secureStorage.setBiometricEnrollmentNeeded(false);
            notifyListeners();
          }
        }
      }
    }
  }

  /// Log out (lock the app)
  void lockApp() {
    if (_isLockEnabled) {
      _isAuthenticated = false;
      notifyListeners();
    }
  }

  /// Force authentication for archive access (used when navigating back to home)
  void forceArchiveAuthentication() {
    _isArchiveAuthenticated = true;
    notifyListeners();
  }
}
