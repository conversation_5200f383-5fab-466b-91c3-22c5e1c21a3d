import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_lock_provider.dart';
import '../services/memory_service.dart';

/// A widget that listens to app lifecycle events to manage app lock state
/// and memory optimization
///
/// This widget handles when the app goes to background and when it returns to
/// foreground to properly enforce app lock timeout settings and optimize memory
class AppLifecycleManager extends StatefulWidget {
  final Widget child;

  const AppLifecycleManager({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<AppLifecycleManager> createState() => _AppLifecycleManagerState();
}

class _AppLifecycleManagerState extends State<AppLifecycleManager> with WidgetsBindingObserver {
  late AppLockProvider _appLockProvider;
  late MemoryService _memoryService;
  
  // Track orientation changes
  DateTime? _lastMetricsChangeTime;
  
  // Track app lifecycle states with timestamps
  DateTime? _lastPausedTime;
  DateTime? _lastResumedTime;
  
  // Flag to track if we should force authentication on next resume
  bool _forceAuthOnNextResume = false;
  
  @override
  void initState() {
    super.initState();
    // Register for lifecycle events
    WidgetsBinding.instance.addObserver(this);
    
    // Initialize providers immediately
    Future.microtask(() {
      _appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
      _memoryService = Provider.of<MemoryService>(context, listen: false);
      _appLockProvider.initialize();
    });
  }

  @override
  void dispose() {
    // Unregister for lifecycle events
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
  
  @override
  void didChangeMetrics() {
    // This is called when screen metrics change, including orientation changes
    _lastMetricsChangeTime = DateTime.now();
    super.didChangeMetrics();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Get provider references again in case they weren't initialized in initState
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
    final memoryService = Provider.of<MemoryService>(context, listen: false);
    
    final now = DateTime.now();
    
    switch (state) {
      case AppLifecycleState.paused:
        // App is going to background
        _lastPausedTime = now;
        
        // Always mark that we need to authenticate when coming back from background
        // This ensures consistency regardless of orientation
        _forceAuthOnNextResume = true;
        
        // Tell the app lock provider we're going to background
        appLockProvider.appToBackground();
        
        // Clean up memory
        memoryService.cleanupCache();
        
        print("DEBUG: AppLifecycleManager: App PAUSED at $_lastPausedTime");
        break;
        
      case AppLifecycleState.inactive:
        // App is partially obscured - this could be due to system UI, dialogs, etc.
        // We don't take any action here to avoid false positives
        print("DEBUG: AppLifecycleManager: App INACTIVE at $now");
        break;
        
      case AppLifecycleState.detached:
        // App is completely detached (terminated)
        memoryService.clearCache();
        memoryService.clearImageCache();
        print("DEBUG: AppLifecycleManager: App DETACHED at $now");
        break;
        
      case AppLifecycleState.resumed:
        _lastResumedTime = now;
        print("DEBUG: AppLifecycleManager: App RESUMED at $_lastResumedTime");
        
        // Check if this is a true app resume from background
        bool isTrueResume = _forceAuthOnNextResume;
        
        // If we have both pause and resume timestamps, check the duration
        if (_lastPausedTime != null && _lastResumedTime != null) {
          final pauseDuration = _lastResumedTime!.difference(_lastPausedTime!);
          
          // If the app was paused for less than 300ms, it's likely not a true background/foreground transition
          if (pauseDuration.inMilliseconds < 300) {
            print("DEBUG: AppLifecycleManager: Very short pause duration (${pauseDuration.inMilliseconds}ms), likely not a true background/foreground");
            isTrueResume = false;
          } else {
            print("DEBUG: AppLifecycleManager: Significant pause duration (${pauseDuration.inMilliseconds}ms), treating as true background/foreground");
          }
        }
        
        // If this resume happens very close to a metrics change, it might be an orientation change
        if (_lastMetricsChangeTime != null) {
          final timeSinceMetricsChange = now.difference(_lastMetricsChangeTime!);
          if (timeSinceMetricsChange.inMilliseconds < 500) {
            print("DEBUG: AppLifecycleManager: Resume very close to metrics change (${timeSinceMetricsChange.inMilliseconds}ms), might be orientation change");
            // But don't override a true resume - if we've been backgrounded, we should still authenticate
            if (!_forceAuthOnNextResume) {
              isTrueResume = false;
            }
          }
        }
        
        // If this is a true resume, tell the app lock provider
        if (isTrueResume) {
          print("DEBUG: AppLifecycleManager: Treating as TRUE resume, checking authentication");
          appLockProvider.appToForeground();
          // Reset the flag
          _forceAuthOnNextResume = false;
        } else {
          print("DEBUG: AppLifecycleManager: Not treating as true resume, skipping authentication check");
        }
        break;
        
      case AppLifecycleState.hidden:
        // App is hidden but still running
        memoryService.clearImageCache();
        print("DEBUG: AppLifecycleManager: App HIDDEN at $now");
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
