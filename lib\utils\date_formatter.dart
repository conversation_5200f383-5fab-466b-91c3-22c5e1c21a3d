import 'package:intl/intl.dart';
import '../models/note.dart';

class DateFormatter {
  static String formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateToCheck = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (dateToCheck == today) {
      return 'Today, ${DateFormat.jm().format(dateTime)}';
    } else if (dateToCheck == yesterday) {
      return 'Yesterday, ${DateFormat.jm().format(dateTime)}';
    } else if (dateToCheck.isAfter(today.subtract(const Duration(days: 7)))) {
      return '${DateFormat.EEEE().format(dateTime)}, ${DateFormat.jm().format(dateTime)}';
    } else {
      return DateFormat.yMMMd().add_jm().format(dateTime);
    }
  }

  static String formatTime(DateTime dateTime) {
    return DateFormat.jm().format(dateTime);
  }

  static String formatReminder(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateToCheck = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (dateToCheck == today) {
      return 'Today at ${DateFormat.jm().format(dateTime)}';
    } else if (dateToCheck == tomorrow) {
      return 'Tomorrow at ${DateFormat.jm().format(dateTime)}';
    } else {
      return DateFormat.yMMMd().add_jm().format(dateTime);
    }
  }

  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else {
      return DateFormat.yMMMd().format(dateTime);
    }
  }

  // Format the repeat type in a user-friendly way
  static String formatRepeatType(ReminderRepeatType repeatType) {
    switch (repeatType) {
      case ReminderRepeatType.daily:
        return 'Every day';
      case ReminderRepeatType.weekly:
        return 'Every week';
      case ReminderRepeatType.monthly:
        return 'Every month';
      case ReminderRepeatType.yearly:
        return 'Every year';
      case ReminderRepeatType.none:
      default:
        return 'Does not repeat';
    }
  }
}
